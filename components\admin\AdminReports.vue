<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">举报管理</h1>
        <p class="text-gray-600">处理用户举报的内容和评论</p>
      </div>
      <div class="flex items-center space-x-2">
        <span class="badge badge-error">{{ pendingReports }} 待处理</span>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="card">
      <div class="card-content p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">举报状态</label>
            <select v-model="statusFilter" @change="loadReports" class="input">
              <option value="">全部状态</option>
              <option value="PENDING">待处理</option>
              <option value="RESOLVED">已处理</option>
              <option value="REJECTED">已驳回</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">举报类型</label>
            <select v-model="typeFilter" @change="loadReports" class="input">
              <option value="">全部类型</option>
              <option value="SPAM">垃圾信息</option>
              <option value="HARASSMENT">骚扰</option>
              <option value="INAPPROPRIATE">不当内容</option>
              <option value="COPYRIGHT">版权侵犯</option>
              <option value="OTHER">其他</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">被举报内容</label>
            <select v-model="contentTypeFilter" @change="loadReports" class="input">
              <option value="">全部内容</option>
              <option value="ARTICLE">文章</option>
              <option value="COMMENT">评论</option>
              <option value="USER">用户</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">举报时间</label>
            <select v-model="timeFilter" @change="loadReports" class="input">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 举报列表 -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-4 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="reports.length === 0" class="text-center py-12">
          <FlagIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-600">没有找到举报记录</p>
        </div>

        <div v-else class="space-y-6">
          <div v-for="report in reports" :key="report.id" class="border border-gray-200 rounded-lg p-6">
            <div class="space-y-4">
              <!-- 举报信息头部 -->
              <div class="flex items-start justify-between">
                <div class="flex items-center space-x-4">
                  <div class="avatar w-10 h-10">
                    <img :src="report.reporter?.avatar || '/placeholder.svg?height=40&width=40'" :alt="report.reporter?.name" />
                  </div>
                  <div>
                    <p class="font-medium">{{ report.reporter?.name || '匿名用户' }}</p>
                    <p class="text-sm text-gray-500">{{ formatDate(report.createdAt) }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span :class="[
                      'badge',
                      report.status === 'PENDING' ? 'badge-warning' : 
                      report.status === 'RESOLVED' ? 'badge-success' : 'badge-error'
                    ]">
                      {{ getStatusText(report.status) }}
                    </span>
                    <span class="badge badge-outline">
                      {{ getTypeText(report.type) }}
                    </span>
                    <span class="badge badge-secondary">
                      {{ getContentTypeText(report.targetType) }}
                    </span>
                  </div>
                </div>
                
                <div v-if="report.status === 'PENDING'" class="flex items-center space-x-2">
                  <button
                    @click="handleReport(report, 'RESOLVED')"
                    class="btn btn-success btn-sm"
                  >
                    处理
                  </button>
                  <button
                    @click="handleReport(report, 'REJECTED')"
                    class="btn btn-error btn-sm"
                  >
                    驳回
                  </button>
                </div>
              </div>
              
              <!-- 举报原因 -->
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 class="font-medium text-yellow-800 mb-2">举报原因</h4>
                <p class="text-yellow-700">{{ report.reason || '无具体说明' }}</p>
              </div>
              
              <!-- 被举报内容 -->
              <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div class="flex items-start justify-between mb-3">
                  <h4 class="font-medium text-gray-800">被举报内容</h4>
                  <button
                    @click="viewTargetContent(report)"
                    class="btn btn-ghost btn-sm"
                  >
                    <EyeIcon class="h-4 w-4 mr-1" />
                    查看详情
                  </button>
                </div>
                
                <div v-if="report.targetType === 'USER'" class="flex items-center space-x-3">
                  <div class="avatar w-12 h-12">
                    <img :src="report.targetUser?.avatar || '/placeholder.svg?height=48&width=48'" :alt="report.targetUser?.name" />
                  </div>
                  <div>
                    <p class="font-medium">{{ report.targetUser?.name || '未知用户' }}</p>
                    <p class="text-sm text-gray-500">{{ report.targetUser?.email }}</p>
                  </div>
                </div>
                
                <div v-else>
                  <div class="flex items-start space-x-3">
                    <div class="avatar w-10 h-10">
                      <img :src="report.targetContent?.author?.avatar || '/placeholder.svg?height=40&width=40'" :alt="report.targetContent?.author?.name" />
                    </div>
                    <div class="flex-1">
                      <p class="font-medium">{{ report.targetContent?.author?.name || '匿名用户' }}</p>
                      <p class="text-sm text-gray-600 mt-1 line-clamp-3">
                        {{ report.targetContent?.title || report.targetContent?.content || '内容已删除' }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 处理结果 -->
              <div v-if="report.status !== 'PENDING'" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium text-blue-800">处理结果</h4>
                    <p class="text-blue-700 mt-1">{{ report.handleReason || '已处理' }}</p>
                  </div>
                  <div class="text-right text-sm text-blue-600">
                    <p>处理人：{{ report.handler?.name || '系统' }}</p>
                    <p>处理时间：{{ formatDate(report.handledAt) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between mt-6 px-6 pb-6">
          <p class="text-sm text-gray-700">
            显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalReports) }} 条，
            共 {{ totalReports }} 条记录
          </p>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="btn btn-outline btn-sm"
            >
              上一页
            </button>
            <span class="text-sm">第 {{ currentPage }} / {{ totalPages }} 页</span>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="btn btn-outline btn-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理举报模态框 -->
    <div v-if="showHandleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">
          {{ handleAction === 'RESOLVED' ? '处理举报' : '驳回举报' }}
        </h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ handleAction === 'RESOLVED' ? '处理说明' : '驳回原因' }} *
            </label>
            <textarea
              v-model="handleReason"
              class="textarea"
              rows="4"
              :placeholder="handleAction === 'RESOLVED' ? '请说明如何处理了这个举报...' : '请说明驳回的原因...'"
              required
            ></textarea>
          </div>
          
          <div class="flex items-center justify-end space-x-4">
            <button
              @click="closeHandleModal"
              class="btn btn-outline"
              :disabled="submitting"
            >
              取消
            </button>
            <button
              @click="submitHandle"
              class="btn btn-primary"
              :disabled="!handleReason.trim() || submitting"
            >
              {{ submitting ? '处理中...' : '确认' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { adminAPI } from '../../services/api.js'
import { 
  FlagIcon,
  EyeIcon
} from 'lucide-vue-next'

// 响应式数据
const reports = ref([])
const loading = ref(false)
const submitting = ref(false)

// 筛选器
const statusFilter = ref('')
const typeFilter = ref('')
const contentTypeFilter = ref('')
const timeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalReports = ref(0)
const totalPages = ref(0)

// 处理模态框
const showHandleModal = ref(false)
const selectedReport = ref(null)
const handleAction = ref('')
const handleReason = ref('')

// 计算待处理举报数量
const pendingReports = computed(() => {
  return reports.value.filter(r => r.status === 'PENDING').length
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'RESOLVED': '已处理',
    'REJECTED': '已驳回'
  }
  return statusMap[status] || status
}

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    'SPAM': '垃圾信息',
    'HARASSMENT': '骚扰',
    'INAPPROPRIATE': '不当内容',
    'COPYRIGHT': '版权侵犯',
    'OTHER': '其他'
  }
  return typeMap[type] || type
}

// 获取内容类型文本
const getContentTypeText = (type) => {
  const typeMap = {
    'ARTICLE': '文章',
    'COMMENT': '评论',
    'USER': '用户'
  }
  return typeMap[type] || type
}

// 加载举报列表
const loadReports = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      status: statusFilter.value,
      type: typeFilter.value,
      targetType: contentTypeFilter.value,
      timeRange: timeFilter.value
    }
    
    const response = await adminAPI.getReports(params)
    
    if (response.code === 0 && response.data) {
      if (Array.isArray(response.data)) {
        reports.value = response.data
        totalReports.value = response.data.length
        totalPages.value = 1
      } else {
        reports.value = response.data.content || []
        totalReports.value = response.data.totalElements || 0
        totalPages.value = response.data.totalPages || 0
      }
    }
  } catch (err) {
    console.error('Failed to load reports:', err)
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadReports()
  }
}

// 查看被举报内容
const viewTargetContent = (report) => {
  if (report.targetType === 'ARTICLE') {
    window.open(`/articles/${report.targetId}`, '_blank')
  } else if (report.targetType === 'USER') {
    window.open(`/users/${report.targetId}`, '_blank')
  }
  // 评论的话可能需要跳转到对应文章的评论区
}

// 处理举报
const handleReport = (report, action) => {
  selectedReport.value = report
  handleAction.value = action
  handleReason.value = ''
  showHandleModal.value = true
}

// 提交处理结果
const submitHandle = async () => {
  if (!handleReason.value.trim()) return

  try {
    submitting.value = true
    
    const response = await adminAPI.handleReport(
      selectedReport.value.id,
      handleAction.value,
      handleReason.value.trim()
    )
    
    if (response.code === 0) {
      // 更新本地数据
      selectedReport.value.status = handleAction.value
      selectedReport.value.handleReason = handleReason.value.trim()
      selectedReport.value.handledAt = new Date().toISOString()
      
      closeHandleModal()
    } else {
      throw new Error(response.message || '处理失败')
    }
  } catch (err) {
    console.error('Failed to handle report:', err)
    alert(err.message || '处理失败')
  } finally {
    submitting.value = false
  }
}

// 关闭处理模态框
const closeHandleModal = () => {
  showHandleModal.value = false
  selectedReport.value = null
  handleAction.value = ''
  handleReason.value = ''
}

onMounted(() => {
  loadReports()
})
</script>
