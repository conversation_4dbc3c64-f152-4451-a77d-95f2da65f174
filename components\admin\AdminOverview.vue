<template>
  <div class="space-y-8">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">数据概览</h1>
      <p class="text-gray-600">系统运营数据总览</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div v-for="stat in stats" :key="stat.title" class="card">
        <div class="card-content p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
              <p :class="[
                'text-sm',
                stat.change >= 0 ? 'text-green-600' : 'text-red-600'
              ]">
                {{ stat.change >= 0 ? '+' : '' }}{{ stat.change }}% 较昨日
              </p>
            </div>
            <div :class="[
              'p-3 rounded-full',
              stat.bgColor
            ]">
              <component :is="stat.icon" :class="['h-6 w-6', stat.iconColor]" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 用户增长趋势 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-semibold">用户增长趋势</h3>
        </div>
        <div class="card-content p-6">
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <p class="text-gray-500">图表区域 - 可集成 Chart.js 或其他图表库</p>
          </div>
        </div>
      </div>

      <!-- 内容发布统计 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-semibold">内容发布统计</h3>
        </div>
        <div class="card-content p-6">
          <div class="space-y-4">
            <div v-for="item in contentStats" :key="item.type" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div :class="['w-3 h-3 rounded-full', item.color]"></div>
                <span class="text-sm font-medium">{{ item.type }}</span>
              </div>
              <div class="text-right">
                <p class="text-sm font-bold">{{ item.count }}</p>
                <p class="text-xs text-gray-500">{{ item.percentage }}%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 最新用户 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-semibold">最新注册用户</h3>
        </div>
        <div class="card-content p-6">
          <div v-if="loading" class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
          <div v-else class="space-y-4">
            <div v-for="user in recentUsers" :key="user.id" class="flex items-center space-x-3">
              <div class="avatar w-10 h-10">
                <img :src="user.avatar || '/placeholder.svg?height=40&width=40'" :alt="user.name" />
              </div>
              <div class="flex-1">
                <p class="font-medium">{{ user.name }}</p>
                <p class="text-sm text-gray-500">{{ formatDate(user.createdAt) }}</p>
              </div>
              <span :class="[
                'badge',
                user.status === 'ACTIVE' ? 'badge-success' : 'badge-secondary'
              ]">
                {{ user.status === 'ACTIVE' ? '正常' : '待激活' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新内容 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-semibold">最新发布内容</h3>
        </div>
        <div class="card-content p-6">
          <div v-if="loading" class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
          <div v-else class="space-y-4">
            <div v-for="content in recentContent" :key="content.id" class="space-y-2">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <p class="font-medium line-clamp-1">{{ content.title || content.content.substring(0, 50) + '...' }}</p>
                  <p class="text-sm text-gray-500">
                    {{ content.author?.name }} · {{ formatDate(content.createdAt) }}
                  </p>
                </div>
                <span :class="[
                  'badge text-xs',
                  content.type === 'ARTICLE' ? 'badge-primary' : 'badge-secondary'
                ]">
                  {{ content.type === 'ARTICLE' ? '文章' : '动态' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">系统状态</h3>
      </div>
      <div class="card-content p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <CheckCircleIcon class="h-8 w-8 text-green-600" />
            </div>
            <p class="font-medium">服务状态</p>
            <p class="text-sm text-green-600">正常运行</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <DatabaseIcon class="h-8 w-8 text-blue-600" />
            </div>
            <p class="font-medium">数据库</p>
            <p class="text-sm text-blue-600">连接正常</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <ServerIcon class="h-8 w-8 text-yellow-600" />
            </div>
            <p class="font-medium">服务器负载</p>
            <p class="text-sm text-yellow-600">中等</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../../services/api.js'
import { 
  UsersIcon,
  FileTextIcon,
  MessageCircleIcon,
  EyeIcon,
  CheckCircleIcon,
  DatabaseIcon,
  ServerIcon
} from 'lucide-vue-next'

const loading = ref(false)
const stats = ref([
  {
    title: '总用户数',
    value: '0',
    change: 0,
    icon: UsersIcon,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    title: '总文章数',
    value: '0',
    change: 0,
    icon: FileTextIcon,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    title: '总评论数',
    value: '0',
    change: 0,
    icon: MessageCircleIcon,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  {
    title: '今日访问',
    value: '0',
    change: 0,
    icon: EyeIcon,
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600'
  }
])

const contentStats = ref([
  { type: '技术文章', count: 0, percentage: 0, color: 'bg-blue-500' },
  { type: '经验分享', count: 0, percentage: 0, color: 'bg-green-500' },
  { type: '问答讨论', count: 0, percentage: 0, color: 'bg-purple-500' },
  { type: '其他内容', count: 0, percentage: 0, color: 'bg-gray-500' }
])

const recentUsers = ref([])
const recentContent = ref([])

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 加载统计数据
const loadStats = async () => {
  try {
    loading.value = true
    
    const response = await adminAPI.getSystemStats()
    
    if (response.code === 0 && response.data) {
      const data = response.data
      
      // 更新统计数据
      stats.value[0].value = data.totalUsers || 0
      stats.value[0].change = data.userGrowth || 0
      
      stats.value[1].value = data.totalArticles || 0
      stats.value[1].change = data.articleGrowth || 0
      
      stats.value[2].value = data.totalComments || 0
      stats.value[2].change = data.commentGrowth || 0
      
      stats.value[3].value = data.todayViews || 0
      stats.value[3].change = data.viewGrowth || 0
      
      // 更新内容统计
      if (data.contentStats) {
        contentStats.value = data.contentStats
      }
    }
  } catch (err) {
    console.error('Failed to load stats:', err)
  } finally {
    loading.value = false
  }
}

// 加载最新用户
const loadRecentUsers = async () => {
  try {
    const response = await adminAPI.getRecentUsers(5)
    
    if (response.code === 0 && response.data) {
      recentUsers.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (err) {
    console.error('Failed to load recent users:', err)
  }
}

// 加载最新内容
const loadRecentContent = async () => {
  try {
    const response = await adminAPI.getRecentContent(5)
    
    if (response.code === 0 && response.data) {
      recentContent.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (err) {
    console.error('Failed to load recent content:', err)
  }
}

onMounted(() => {
  loadStats()
  loadRecentUsers()
  loadRecentContent()
})
</script>
