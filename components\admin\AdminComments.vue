<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">评论管理</h1>
        <p class="text-gray-600">管理系统中的所有评论</p>
      </div>
      <div class="flex items-center space-x-4">
        <button @click="refreshComments" :disabled="loading" class="btn btn-outline">
          <RefreshCwIcon :class="['h-4 w-4 mr-2', loading ? 'animate-spin' : '']" />
          刷新
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="card-content p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">搜索评论</label>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="评论内容..."
              class="input"
              @input="debouncedSearch"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">评论状态</label>
            <select v-model="statusFilter" @change="loadComments" class="input">
              <option value="">全部状态</option>
              <option value="NORMAL">正常</option>
              <option value="HIDDEN">已隐藏</option>
              <option value="REPORTED">被举报</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">内容类型</label>
            <select v-model="contentTypeFilter" @change="loadComments" class="input">
              <option value="">全部类型</option>
              <option value="ARTICLE">文章评论</option>
              <option value="DYNAMIC">动态评论</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
            <select v-model="timeFilter" @change="loadComments" class="input">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedComments.length > 0" class="card">
      <div class="card-content p-4">
        <div class="flex items-center justify-between">
          <p class="text-sm text-gray-600">已选择 {{ selectedComments.length }} 条评论</p>
          <div class="flex items-center space-x-2">
            <button @click="batchHide" class="btn btn-warning btn-sm">
              批量隐藏
            </button>
            <button @click="batchDelete" class="btn btn-error btn-sm">
              批量删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-4 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="comments.length === 0" class="text-center py-12">
          <MessageCircleIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-600">没有找到评论</p>
        </div>

        <div v-else class="space-y-4">
          <div v-for="comment in comments" :key="comment.id" class="border border-gray-200 rounded-lg p-6">
            <div class="flex items-start space-x-4">
              <input
                type="checkbox"
                :value="comment.id"
                v-model="selectedComments"
                class="mt-1"
              />
              
              <div class="flex-1 space-y-3">
                <div class="flex items-start justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="avatar w-10 h-10">
                      <img :src="comment.author?.avatar || '/placeholder.svg?height=40&width=40'" :alt="comment.author?.name" />
                    </div>
                    <div>
                      <p class="font-medium">{{ comment.author?.name || '匿名用户' }}</p>
                      <p class="text-sm text-gray-500">{{ formatDate(comment.createdAt) }}</p>
                    </div>
                    <span :class="[
                      'badge text-xs',
                      comment.status === 'NORMAL' ? 'badge-success' : 
                      comment.status === 'HIDDEN' ? 'badge-warning' : 'badge-error'
                    ]">
                      {{ getStatusText(comment.status) }}
                    </span>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <button
                      @click="viewContent(comment)"
                      class="btn btn-ghost btn-sm"
                      title="查看原文"
                    >
                      <EyeIcon class="h-4 w-4" />
                    </button>
                    <button
                      v-if="comment.status === 'NORMAL'"
                      @click="hideComment(comment)"
                      class="btn btn-warning btn-sm"
                      title="隐藏评论"
                    >
                      <EyeOffIcon class="h-4 w-4" />
                    </button>
                    <button
                      v-else-if="comment.status === 'HIDDEN'"
                      @click="showComment(comment)"
                      class="btn btn-success btn-sm"
                      title="显示评论"
                    >
                      <EyeIcon class="h-4 w-4" />
                    </button>
                    <button
                      @click="deleteComment(comment)"
                      class="btn btn-error btn-sm"
                      title="删除评论"
                    >
                      <TrashIcon class="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-4">
                  <p class="text-gray-800 whitespace-pre-wrap">{{ comment.content || comment.commentText }}</p>
                </div>
                
                <div class="flex items-center justify-between text-sm text-gray-500">
                  <div class="flex items-center space-x-4">
                    <span>{{ comment.likeCount || 0 }} 点赞</span>
                    <span>{{ comment.replyCount || 0 }} 回复</span>
                    <span v-if="comment.reportCount > 0" class="text-red-600">
                      {{ comment.reportCount }} 次举报
                    </span>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <span class="badge badge-outline text-xs">
                      {{ comment.contentType === 'ARTICLE' ? '文章评论' : '动态评论' }}
                    </span>
                    <router-link
                      :to="comment.contentType === 'ARTICLE' ? `/articles/${comment.contentId}` : `/dynamics/${comment.contentId}`"
                      class="text-blue-600 hover:underline"
                    >
                      查看原文
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between mt-6 px-6 pb-6">
          <p class="text-sm text-gray-700">
            显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalComments) }} 条，
            共 {{ totalComments }} 条记录
          </p>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="btn btn-outline btn-sm"
            >
              上一页
            </button>
            <span class="text-sm">第 {{ currentPage }} / {{ totalPages }} 页</span>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="btn btn-outline btn-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../../services/api.js'
import { 
  MessageCircleIcon,
  RefreshCwIcon,
  EyeIcon,
  EyeOffIcon,
  TrashIcon
} from 'lucide-vue-next'

// 响应式数据
const comments = ref([])
const loading = ref(false)
const selectedComments = ref([])

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const contentTypeFilter = ref('')
const timeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalComments = ref(0)
const totalPages = ref(0)

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadComments()
  }, 500)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'NORMAL': '正常',
    'HIDDEN': '已隐藏',
    'REPORTED': '被举报'
  }
  return statusMap[status] || status
}

// 加载评论列表
const loadComments = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value,
      contentType: contentTypeFilter.value,
      timeRange: timeFilter.value
    }
    
    const response = await adminAPI.getComments(params)
    
    if (response.code === 0 && response.data) {
      if (Array.isArray(response.data)) {
        comments.value = response.data
        totalComments.value = response.data.length
        totalPages.value = 1
      } else {
        comments.value = response.data.content || []
        totalComments.value = response.data.totalElements || 0
        totalPages.value = response.data.totalPages || 0
      }
    }
  } catch (err) {
    console.error('Failed to load comments:', err)
  } finally {
    loading.value = false
  }
}

// 刷新评论
const refreshComments = () => {
  loadComments()
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadComments()
  }
}

// 查看原文
const viewContent = (comment) => {
  const url = comment.contentType === 'ARTICLE' 
    ? `/articles/${comment.contentId}` 
    : `/dynamics/${comment.contentId}`
  window.open(url, '_blank')
}

// 隐藏评论
const hideComment = async (comment) => {
  if (!confirm('确定要隐藏这条评论吗？')) return

  try {
    // 这里需要一个隐藏评论的API
    // 暂时使用删除API的逻辑
    comment.status = 'HIDDEN'
  } catch (err) {
    console.error('Failed to hide comment:', err)
    alert('操作失败')
  }
}

// 显示评论
const showComment = async (comment) => {
  try {
    comment.status = 'NORMAL'
  } catch (err) {
    console.error('Failed to show comment:', err)
    alert('操作失败')
  }
}

// 删除评论
const deleteComment = async (comment) => {
  if (!confirm('确定要删除这条评论吗？此操作不可恢复！')) return

  try {
    const response = await adminAPI.deleteComment(comment.id)
    
    if (response.code === 0) {
      loadComments()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (err) {
    console.error('Failed to delete comment:', err)
    alert(err.message || '删除失败')
  }
}

// 批量隐藏
const batchHide = async () => {
  if (!confirm(`确定要批量隐藏 ${selectedComments.value.length} 条评论吗？`)) return

  try {
    // 这里需要批量隐藏的API
    selectedComments.value = []
    loadComments()
  } catch (err) {
    console.error('Failed to batch hide:', err)
    alert('批量操作失败')
  }
}

// 批量删除
const batchDelete = async () => {
  if (!confirm(`确定要批量删除 ${selectedComments.value.length} 条评论吗？此操作不可恢复！`)) return

  try {
    const response = await adminAPI.batchDeleteComments(selectedComments.value)
    
    if (response.code === 0) {
      selectedComments.value = []
      loadComments()
    } else {
      throw new Error(response.message || '批量操作失败')
    }
  } catch (err) {
    console.error('Failed to batch delete:', err)
    alert(err.message || '批量操作失败')
  }
}

onMounted(() => {
  loadComments()
})
</script>
