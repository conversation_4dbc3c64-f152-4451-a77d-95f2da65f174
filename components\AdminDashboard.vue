<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader />

    <div class="flex">
      <!-- 侧边栏 -->
      <div class="w-64 bg-white shadow-sm min-h-screen">
        <div class="p-6">
          <h2 class="text-xl font-bold text-gray-900 mb-6">管理后台</h2>
          
          <nav class="space-y-2">
            <button
              v-for="item in menuItems"
              :key="item.key"
              @click="activeTab = item.key"
              :class="[
                'w-full flex items-center px-4 py-3 text-left rounded-lg transition',
                activeTab === item.key ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700' : 'text-gray-700 hover:bg-gray-50'
              ]"
            >
              <component :is="item.icon" class="h-5 w-5 mr-3" />
              {{ item.label }}
              <span v-if="item.badge" class="ml-auto badge badge-primary">
                {{ item.badge }}
              </span>
            </button>
          </nav>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="flex-1 p-8">
        <!-- 概览页面 -->
        <div v-if="activeTab === 'overview'">
          <AdminOverview />
        </div>

        <!-- 用户管理 -->
        <div v-else-if="activeTab === 'users'">
          <AdminUsers />
        </div>

        <!-- 内容管理 -->
        <div v-else-if="activeTab === 'content'">
          <AdminContent />
        </div>

        <!-- 评论管理 -->
        <div v-else-if="activeTab === 'comments'">
          <AdminComments />
        </div>

        <!-- 举报管理 -->
        <div v-else-if="activeTab === 'reports'">
          <AdminReports />
        </div>

        <!-- 系统设置 -->
        <div v-else-if="activeTab === 'settings'">
          <AdminSettings />
        </div>

        <!-- 日志管理 -->
        <div v-else-if="activeTab === 'logs'">
          <AdminLogs />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import { useRouter } from 'vue-router'
import AppHeader from './AppHeader.vue'
import AdminOverview from './admin/AdminOverview.vue'
import AdminUsers from './admin/AdminUsers.vue'
import AdminContent from './admin/AdminContent.vue'
import AdminComments from './admin/AdminComments.vue'
import AdminReports from './admin/AdminReports.vue'
import AdminSettings from './admin/AdminSettings.vue'
import AdminLogs from './admin/AdminLogs.vue'
import { 
  BarChart3Icon,
  UsersIcon,
  FileTextIcon,
  MessageCircleIcon,
  FlagIcon,
  SettingsIcon,
  FileIcon
} from 'lucide-vue-next'

const router = useRouter()
const globalState = inject('globalState')

const activeTab = ref('overview')

// 菜单项
const menuItems = ref([
  {
    key: 'overview',
    label: '数据概览',
    icon: BarChart3Icon,
    badge: null
  },
  {
    key: 'users',
    label: '用户管理',
    icon: UsersIcon,
    badge: null
  },
  {
    key: 'content',
    label: '内容管理',
    icon: FileTextIcon,
    badge: null
  },
  {
    key: 'comments',
    label: '评论管理',
    icon: MessageCircleIcon,
    badge: null
  },
  {
    key: 'reports',
    label: '举报管理',
    icon: FlagIcon,
    badge: 0
  },
  {
    key: 'settings',
    label: '系统设置',
    icon: SettingsIcon,
    badge: null
  },
  {
    key: 'logs',
    label: '操作日志',
    icon: FileIcon,
    badge: null
  }
])

// 检查管理员权限
const checkAdminPermission = () => {
  if (!globalState.isLoggedIn || !globalState.isAdmin) {
    router.push('/')
    return false
  }
  return true
}

onMounted(() => {
  checkAdminPermission()
})
</script>
