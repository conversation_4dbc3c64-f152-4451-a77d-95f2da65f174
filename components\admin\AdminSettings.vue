<template>
  <div class="space-y-8">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">系统设置</h1>
      <p class="text-gray-600">管理系统的各项配置</p>
    </div>

    <!-- 基本设置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">基本设置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">网站名称</label>
            <input
              v-model="settings.siteName"
              type="text"
              class="input"
              placeholder="论坛网站"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">网站描述</label>
            <input
              v-model="settings.siteDescription"
              type="text"
              class="input"
              placeholder="一个优秀的技术论坛"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">联系邮箱</label>
            <input
              v-model="settings.contactEmail"
              type="email"
              class="input"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">ICP备案号</label>
            <input
              v-model="settings.icpNumber"
              type="text"
              class="input"
              placeholder="京ICP备xxxxxxxx号"
            />
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">网站公告</label>
          <textarea
            v-model="settings.announcement"
            class="textarea"
            rows="4"
            placeholder="发布重要通知..."
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 用户设置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">用户设置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">允许用户注册</label>
              <p class="text-sm text-gray-500">关闭后新用户无法注册</p>
            </div>
            <input
              v-model="settings.allowRegistration"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">邮箱验证</label>
              <p class="text-sm text-gray-500">注册时需要验证邮箱</p>
            </div>
            <input
              v-model="settings.requireEmailVerification"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">实名认证</label>
              <p class="text-sm text-gray-500">发布内容需要实名认证</p>
            </div>
            <input
              v-model="settings.requireRealName"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">新用户审核</label>
              <p class="text-sm text-gray-500">新注册用户需要管理员审核</p>
            </div>
            <input
              v-model="settings.requireUserApproval"
              type="checkbox"
              class="toggle"
            />
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">每日发帖限制</label>
            <input
              v-model.number="settings.dailyPostLimit"
              type="number"
              class="input"
              min="0"
              placeholder="10"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">每日评论限制</label>
            <input
              v-model.number="settings.dailyCommentLimit"
              type="number"
              class="input"
              min="0"
              placeholder="50"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 内容设置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">内容设置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">内容审核</label>
              <p class="text-sm text-gray-500">新发布的内容需要审核</p>
            </div>
            <input
              v-model="settings.requireContentApproval"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">敏感词过滤</label>
              <p class="text-sm text-gray-500">自动过滤敏感词汇</p>
            </div>
            <input
              v-model="settings.enableWordFilter"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">允许匿名发布</label>
              <p class="text-sm text-gray-500">用户可以匿名发布内容</p>
            </div>
            <input
              v-model="settings.allowAnonymous"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">水印保护</label>
              <p class="text-sm text-gray-500">上传图片自动添加水印</p>
            </div>
            <input
              v-model="settings.enableWatermark"
              type="checkbox"
              class="toggle"
            />
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">文件上传大小限制 (MB)</label>
            <input
              v-model.number="settings.maxFileSize"
              type="number"
              class="input"
              min="1"
              max="100"
              placeholder="10"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">允许的文件类型</label>
            <input
              v-model="settings.allowedFileTypes"
              type="text"
              class="input"
              placeholder="jpg,png,gif,pdf,doc"
            />
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">敏感词列表</label>
          <textarea
            v-model="settings.sensitiveWords"
            class="textarea"
            rows="4"
            placeholder="每行一个敏感词..."
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 邮件设置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">邮件设置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">SMTP服务器</label>
            <input
              v-model="settings.smtpHost"
              type="text"
              class="input"
              placeholder="smtp.example.com"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">SMTP端口</label>
            <input
              v-model.number="settings.smtpPort"
              type="number"
              class="input"
              placeholder="587"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">发送邮箱</label>
            <input
              v-model="settings.smtpUsername"
              type="email"
              class="input"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">邮箱密码</label>
            <input
              v-model="settings.smtpPassword"
              type="password"
              class="input"
              placeholder="••••••••"
            />
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <label class="font-medium text-gray-900">启用SSL</label>
            <p class="text-sm text-gray-500">使用SSL加密连接</p>
          </div>
          <input
            v-model="settings.smtpSsl"
            type="checkbox"
            class="toggle"
          />
        </div>
        
        <div class="flex items-center space-x-4">
          <button @click="testEmail" :disabled="testingEmail" class="btn btn-outline">
            {{ testingEmail ? '测试中...' : '测试邮件发送' }}
          </button>
          <span v-if="emailTestResult" :class="[
            'text-sm',
            emailTestResult.success ? 'text-green-600' : 'text-red-600'
          ]">
            {{ emailTestResult.message }}
          </span>
        </div>
      </div>
    </div>

    <!-- 安全设置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">安全设置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">启用验证码</label>
              <p class="text-sm text-gray-500">登录和注册时需要验证码</p>
            </div>
            <input
              v-model="settings.enableCaptcha"
              type="checkbox"
              class="toggle"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">IP访问限制</label>
              <p class="text-sm text-gray-500">限制单个IP的访问频率</p>
            </div>
            <input
              v-model="settings.enableRateLimit"
              type="checkbox"
              class="toggle"
            />
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">登录失败锁定次数</label>
            <input
              v-model.number="settings.maxLoginAttempts"
              type="number"
              class="input"
              min="3"
              max="10"
              placeholder="5"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">锁定时间 (分钟)</label>
            <input
              v-model.number="settings.lockoutDuration"
              type="number"
              class="input"
              min="5"
              max="60"
              placeholder="15"
            />
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">IP黑名单</label>
          <textarea
            v-model="settings.ipBlacklist"
            class="textarea"
            rows="4"
            placeholder="每行一个IP地址..."
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="flex items-center justify-end space-x-4">
      <button @click="resetSettings" class="btn btn-outline" :disabled="saving">
        重置
      </button>
      <button @click="saveSettings" class="btn btn-primary" :disabled="saving">
        {{ saving ? '保存中...' : '保存设置' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../../services/api.js'

// 响应式数据
const saving = ref(false)
const testingEmail = ref(false)
const emailTestResult = ref(null)

// 设置数据
const settings = ref({
  // 基本设置
  siteName: '',
  siteDescription: '',
  contactEmail: '',
  icpNumber: '',
  announcement: '',
  
  // 用户设置
  allowRegistration: true,
  requireEmailVerification: true,
  requireRealName: false,
  requireUserApproval: false,
  dailyPostLimit: 10,
  dailyCommentLimit: 50,
  
  // 内容设置
  requireContentApproval: false,
  enableWordFilter: true,
  allowAnonymous: false,
  enableWatermark: false,
  maxFileSize: 10,
  allowedFileTypes: 'jpg,png,gif,pdf,doc',
  sensitiveWords: '',
  
  // 邮件设置
  smtpHost: '',
  smtpPort: 587,
  smtpUsername: '',
  smtpPassword: '',
  smtpSsl: true,
  
  // 安全设置
  enableCaptcha: true,
  enableRateLimit: true,
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  ipBlacklist: ''
})

// 加载设置
const loadSettings = async () => {
  try {
    const response = await adminAPI.getSystemSettings()
    
    if (response.code === 0 && response.data) {
      Object.assign(settings.value, response.data)
    }
  } catch (err) {
    console.error('Failed to load settings:', err)
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    saving.value = true
    
    const response = await adminAPI.updateSystemSettings(settings.value)
    
    if (response.code === 0) {
      alert('设置保存成功')
    } else {
      throw new Error(response.message || '保存失败')
    }
  } catch (err) {
    console.error('Failed to save settings:', err)
    alert(err.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  if (confirm('确定要重置所有设置吗？')) {
    loadSettings()
  }
}

// 测试邮件发送
const testEmail = async () => {
  try {
    testingEmail.value = true
    emailTestResult.value = null
    
    // 这里需要一个测试邮件的API
    // 暂时模拟测试结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emailTestResult.value = {
      success: true,
      message: '测试邮件发送成功'
    }
  } catch (err) {
    emailTestResult.value = {
      success: false,
      message: '测试邮件发送失败: ' + err.message
    }
  } finally {
    testingEmail.value = false
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.toggle {
  @apply relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.toggle:checked {
  @apply bg-blue-600;
}

.toggle::before {
  @apply absolute left-1 top-1 h-4 w-4 rounded-full bg-white transition-transform;
  content: '';
}

.toggle:checked::before {
  @apply translate-x-5;
}
</style>
