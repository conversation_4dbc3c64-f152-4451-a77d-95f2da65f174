<template>
  <div class="image-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-section">
        <h3 class="toolbar-title">图片编辑器</h3>
        <div class="toolbar-actions">
          <button
            v-for="tool in tools"
            :key="tool.name"
            @click="activeTool = tool.name"
            :class="[
              'tool-btn',
              activeTool === tool.name ? 'tool-btn-active' : ''
            ]"
            :title="tool.title"
          >
            <component :is="tool.icon" class="h-4 w-4" />
            <span class="tool-label">{{ tool.label }}</span>
          </button>
        </div>
      </div>
      
      <div class="toolbar-section">
        <div class="toolbar-controls">
          <button @click="undo" :disabled="!canUndo" class="control-btn" title="撤销">
            <UndoIcon class="h-4 w-4" />
          </button>
          <button @click="redo" :disabled="!canRedo" class="control-btn" title="重做">
            <RedoIcon class="h-4 w-4" />
          </button>
          <button @click="reset" class="control-btn" title="重置">
            <RotateCcwIcon class="h-4 w-4" />
          </button>
          <button @click="saveImage" class="control-btn control-btn-primary" title="保存">
            <SaveIcon class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑区域 -->
    <div class="editor-workspace">
      <!-- 侧边栏 -->
      <div class="editor-sidebar">
        <!-- 裁剪工具 -->
        <div v-if="activeTool === 'crop'" class="tool-panel">
          <h4 class="panel-title">裁剪设置</h4>
          <div class="panel-content">
            <div class="control-group">
              <label class="control-label">预设比例</label>
              <div class="aspect-ratio-grid">
                <button
                  v-for="ratio in aspectRatios"
                  :key="ratio.value"
                  @click="setAspectRatio(ratio.value)"
                  :class="[
                    'aspect-btn',
                    cropAspectRatio === ratio.value ? 'aspect-btn-active' : ''
                  ]"
                >
                  {{ ratio.label }}
                </button>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">自定义尺寸</label>
              <div class="size-inputs">
                <input
                  v-model.number="cropWidth"
                  type="number"
                  placeholder="宽度"
                  class="size-input"
                  @change="updateCropSize"
                />
                <span class="size-separator">×</span>
                <input
                  v-model.number="cropHeight"
                  type="number"
                  placeholder="高度"
                  class="size-input"
                  @change="updateCropSize"
                />
              </div>
            </div>
            
            <button @click="applyCrop" class="apply-btn">
              应用裁剪
            </button>
          </div>
        </div>

        <!-- 调整工具 -->
        <div v-if="activeTool === 'adjust'" class="tool-panel">
          <h4 class="panel-title">图像调整</h4>
          <div class="panel-content">
            <div class="control-group">
              <label class="control-label">亮度</label>
              <div class="slider-container">
                <input
                  v-model.number="adjustments.brightness"
                  type="range"
                  min="-100"
                  max="100"
                  class="slider"
                  @input="applyAdjustments"
                />
                <span class="slider-value">{{ adjustments.brightness }}</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">对比度</label>
              <div class="slider-container">
                <input
                  v-model.number="adjustments.contrast"
                  type="range"
                  min="-100"
                  max="100"
                  class="slider"
                  @input="applyAdjustments"
                />
                <span class="slider-value">{{ adjustments.contrast }}</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">饱和度</label>
              <div class="slider-container">
                <input
                  v-model.number="adjustments.saturation"
                  type="range"
                  min="-100"
                  max="100"
                  class="slider"
                  @input="applyAdjustments"
                />
                <span class="slider-value">{{ adjustments.saturation }}</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">色调</label>
              <div class="slider-container">
                <input
                  v-model.number="adjustments.hue"
                  type="range"
                  min="-180"
                  max="180"
                  class="slider"
                  @input="applyAdjustments"
                />
                <span class="slider-value">{{ adjustments.hue }}°</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">模糊</label>
              <div class="slider-container">
                <input
                  v-model.number="adjustments.blur"
                  type="range"
                  min="0"
                  max="10"
                  step="0.1"
                  class="slider"
                  @input="applyAdjustments"
                />
                <span class="slider-value">{{ adjustments.blur }}</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">锐化</label>
              <div class="slider-container">
                <input
                  v-model.number="adjustments.sharpen"
                  type="range"
                  min="0"
                  max="10"
                  step="0.1"
                  class="slider"
                  @input="applyAdjustments"
                />
                <span class="slider-value">{{ adjustments.sharpen }}</span>
              </div>
            </div>
            
            <button @click="resetAdjustments" class="reset-btn">
              重置调整
            </button>
          </div>
        </div>

        <!-- 滤镜工具 -->
        <div v-if="activeTool === 'filter'" class="tool-panel">
          <h4 class="panel-title">滤镜效果</h4>
          <div class="panel-content">
            <div class="filter-grid">
              <div
                v-for="filter in filters"
                :key="filter.name"
                @click="applyFilter(filter)"
                :class="[
                  'filter-item',
                  activeFilter === filter.name ? 'filter-item-active' : ''
                ]"
              >
                <div class="filter-preview" :style="{ filter: filter.css }">
                  <div class="filter-sample"></div>
                </div>
                <span class="filter-name">{{ filter.label }}</span>
              </div>
            </div>
            
            <div v-if="activeFilter && activeFilter !== 'none'" class="control-group">
              <label class="control-label">滤镜强度</label>
              <div class="slider-container">
                <input
                  v-model.number="filterIntensity"
                  type="range"
                  min="0"
                  max="100"
                  class="slider"
                  @input="updateFilterIntensity"
                />
                <span class="slider-value">{{ filterIntensity }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 变换工具 -->
        <div v-if="activeTool === 'transform'" class="tool-panel">
          <h4 class="panel-title">变换操作</h4>
          <div class="panel-content">
            <div class="control-group">
              <label class="control-label">旋转</label>
              <div class="transform-buttons">
                <button @click="rotate(-90)" class="transform-btn">
                  <RotateCcwIcon class="h-4 w-4" />
                  左转90°
                </button>
                <button @click="rotate(90)" class="transform-btn">
                  <RotateCwIcon class="h-4 w-4" />
                  右转90°
                </button>
              </div>
              
              <div class="slider-container">
                <input
                  v-model.number="transformations.rotation"
                  type="range"
                  min="-180"
                  max="180"
                  class="slider"
                  @input="applyTransformations"
                />
                <span class="slider-value">{{ transformations.rotation }}°</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">翻转</label>
              <div class="transform-buttons">
                <button @click="flip('horizontal')" class="transform-btn">
                  <FlipHorizontalIcon class="h-4 w-4" />
                  水平翻转
                </button>
                <button @click="flip('vertical')" class="transform-btn">
                  <FlipVerticalIcon class="h-4 w-4" />
                  垂直翻转
                </button>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">缩放</label>
              <div class="slider-container">
                <input
                  v-model.number="transformations.scale"
                  type="range"
                  min="0.1"
                  max="3"
                  step="0.1"
                  class="slider"
                  @input="applyTransformations"
                />
                <span class="slider-value">{{ transformations.scale }}x</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 文字工具 -->
        <div v-if="activeTool === 'text'" class="tool-panel">
          <h4 class="panel-title">添加文字</h4>
          <div class="panel-content">
            <div class="control-group">
              <label class="control-label">文字内容</label>
              <textarea
                v-model="textOptions.content"
                class="text-input"
                placeholder="输入文字..."
                rows="3"
              ></textarea>
            </div>
            
            <div class="control-group">
              <label class="control-label">字体大小</label>
              <div class="slider-container">
                <input
                  v-model.number="textOptions.fontSize"
                  type="range"
                  min="12"
                  max="72"
                  class="slider"
                />
                <span class="slider-value">{{ textOptions.fontSize }}px</span>
              </div>
            </div>
            
            <div class="control-group">
              <label class="control-label">字体颜色</label>
              <input
                v-model="textOptions.color"
                type="color"
                class="color-input"
              />
            </div>
            
            <div class="control-group">
              <label class="control-label">字体样式</label>
              <div class="font-options">
                <button
                  @click="textOptions.bold = !textOptions.bold"
                  :class="['font-btn', textOptions.bold ? 'font-btn-active' : '']"
                >
                  <BoldIcon class="h-4 w-4" />
                </button>
                <button
                  @click="textOptions.italic = !textOptions.italic"
                  :class="['font-btn', textOptions.italic ? 'font-btn-active' : '']"
                >
                  <ItalicIcon class="h-4 w-4" />
                </button>
                <button
                  @click="textOptions.underline = !textOptions.underline"
                  :class="['font-btn', textOptions.underline ? 'font-btn-active' : '']"
                >
                  <UnderlineIcon class="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <button @click="addText" class="apply-btn">
              添加文字
            </button>
          </div>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="editor-canvas-container">
        <div class="canvas-wrapper" ref="canvasWrapper">
          <canvas
            ref="canvas"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="stopDrawing"
            @click="handleCanvasClick"
          ></canvas>
          
          <!-- 裁剪框 -->
          <div
            v-if="activeTool === 'crop' && showCropBox"
            class="crop-box"
            :style="cropBoxStyle"
          >
            <div class="crop-handles">
              <div
                v-for="handle in cropHandles"
                :key="handle"
                :class="`crop-handle crop-handle-${handle}`"
                @mousedown="startCropResize($event, handle)"
              ></div>
            </div>
          </div>
        </div>
        
        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <button @click="zoomOut" class="zoom-btn">
            <ZoomOutIcon class="h-4 w-4" />
          </button>
          <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
          <button @click="zoomIn" class="zoom-btn">
            <ZoomInIcon class="h-4 w-4" />
          </button>
          <button @click="fitToScreen" class="zoom-btn">
            <MaximizeIcon class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- 保存对话框 -->
    <div v-if="showSaveDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">保存图片</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">文件名</label>
            <input
              v-model="saveOptions.fileName"
              type="text"
              class="input"
              placeholder="输入文件名"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">格式</label>
            <select v-model="saveOptions.format" class="input">
              <option value="png">PNG</option>
              <option value="jpeg">JPEG</option>
              <option value="webp">WebP</option>
            </select>
          </div>
          
          <div v-if="saveOptions.format === 'jpeg'">
            <label class="block text-sm font-medium text-gray-700 mb-2">质量</label>
            <div class="slider-container">
              <input
                v-model.number="saveOptions.quality"
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                class="slider"
              />
              <span class="slider-value">{{ Math.round(saveOptions.quality * 100) }}%</span>
            </div>
          </div>
        </div>
        
        <div class="flex items-center justify-end space-x-4 mt-6">
          <button @click="showSaveDialog = false" class="btn btn-outline">
            取消
          </button>
          <button @click="confirmSave" class="btn btn-primary">
            保存
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import {
  CropIcon,
  SlidersIcon,
  FilterIcon,
  RotateCwIcon,
  TypeIcon,
  UndoIcon,
  RedoIcon,
  RotateCcwIcon,
  SaveIcon,
  ZoomInIcon,
  ZoomOutIcon,
  MaximizeIcon,
  FlipHorizontalIcon,
  FlipVerticalIcon,
  BoldIcon,
  ItalicIcon,
  UnderlineIcon
} from 'lucide-vue-next'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  }
})

const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const canvas = ref(null)
const canvasWrapper = ref(null)
const ctx = ref(null)
const originalImage = ref(null)
const currentImage = ref(null)
const activeTool = ref('crop')
const zoomLevel = ref(1)
const showSaveDialog = ref(false)

// 历史记录
const history = ref([])
const historyIndex = ref(-1)

// 工具配置
const tools = [
  { name: 'crop', label: '裁剪', icon: CropIcon, title: '裁剪图片' },
  { name: 'adjust', label: '调整', icon: SlidersIcon, title: '调整亮度、对比度等' },
  { name: 'filter', label: '滤镜', icon: FilterIcon, title: '应用滤镜效果' },
  { name: 'transform', label: '变换', icon: RotateCwIcon, title: '旋转、翻转、缩放' },
  { name: 'text', label: '文字', icon: TypeIcon, title: '添加文字' }
]

// 裁剪相关
const showCropBox = ref(false)
const cropBox = ref({ x: 0, y: 0, width: 0, height: 0 })
const cropAspectRatio = ref(null)
const cropWidth = ref(0)
const cropHeight = ref(0)
const cropHandles = ['nw', 'n', 'ne', 'w', 'e', 'sw', 's', 'se']

// 预设比例
const aspectRatios = [
  { label: '自由', value: null },
  { label: '1:1', value: 1 },
  { label: '4:3', value: 4/3 },
  { label: '3:2', value: 3/2 },
  { label: '16:9', value: 16/9 },
  { label: '2:1', value: 2 }
]

// 调整参数
const adjustments = ref({
  brightness: 0,
  contrast: 0,
  saturation: 0,
  hue: 0,
  blur: 0,
  sharpen: 0
})

// 滤镜配置
const filters = [
  { name: 'none', label: '无', css: 'none' },
  { name: 'grayscale', label: '黑白', css: 'grayscale(100%)' },
  { name: 'sepia', label: '怀旧', css: 'sepia(100%)' },
  { name: 'vintage', label: '复古', css: 'sepia(50%) contrast(1.2) brightness(1.1)' },
  { name: 'warm', label: '暖色', css: 'sepia(20%) saturate(1.2) hue-rotate(15deg)' },
  { name: 'cool', label: '冷色', css: 'saturate(1.1) hue-rotate(-15deg) brightness(1.1)' },
  { name: 'dramatic', label: '戏剧', css: 'contrast(1.5) saturate(1.3) brightness(0.9)' },
  { name: 'soft', label: '柔和', css: 'contrast(0.8) brightness(1.2) saturate(0.9)' }
]

const activeFilter = ref('none')
const filterIntensity = ref(100)

// 变换参数
const transformations = ref({
  rotation: 0,
  scaleX: 1,
  scaleY: 1,
  scale: 1
})

// 文字选项
const textOptions = ref({
  content: '',
  fontSize: 24,
  color: '#000000',
  bold: false,
  italic: false,
  underline: false
})

// 保存选项
const saveOptions = ref({
  fileName: 'edited-image',
  format: 'png',
  quality: 0.9
})

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

const cropBoxStyle = computed(() => ({
  left: `${cropBox.value.x}px`,
  top: `${cropBox.value.y}px`,
  width: `${cropBox.value.width}px`,
  height: `${cropBox.value.height}px`
}))

// 监听工具切换
watch(activeTool, (newTool) => {
  if (newTool === 'crop') {
    initCropBox()
  } else {
    showCropBox.value = false
  }
})

// 初始化
onMounted(async () => {
  await initCanvas()
  await loadImage()
})

// 初始化画布
const initCanvas = async () => {
  if (!canvas.value) return
  
  ctx.value = canvas.value.getContext('2d')
  canvas.value.width = props.width
  canvas.value.height = props.height
  
  // 设置画布样式
  ctx.value.imageSmoothingEnabled = true
  ctx.value.imageSmoothingQuality = 'high'
}

// 加载图片
const loadImage = async () => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    img.onload = () => {
      originalImage.value = img
      currentImage.value = img
      drawImage()
      saveToHistory()
      resolve()
    }
    img.onerror = reject
    img.src = props.imageUrl
  })
}

// 绘制图片
const drawImage = () => {
  if (!ctx.value || !currentImage.value) return
  
  ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height)
  
  // 计算图片显示尺寸和位置
  const imgAspect = currentImage.value.width / currentImage.value.height
  const canvasAspect = canvas.value.width / canvas.value.height
  
  let drawWidth, drawHeight, drawX, drawY
  
  if (imgAspect > canvasAspect) {
    drawWidth = canvas.value.width * zoomLevel.value
    drawHeight = drawWidth / imgAspect
  } else {
    drawHeight = canvas.value.height * zoomLevel.value
    drawWidth = drawHeight * imgAspect
  }
  
  drawX = (canvas.value.width - drawWidth) / 2
  drawY = (canvas.value.height - drawHeight) / 2
  
  ctx.value.drawImage(currentImage.value, drawX, drawY, drawWidth, drawHeight)
}

// 保存到历史记录
const saveToHistory = () => {
  const imageData = ctx.value.getImageData(0, 0, canvas.value.width, canvas.value.height)
  
  // 删除当前位置之后的历史记录
  history.value = history.value.slice(0, historyIndex.value + 1)
  
  // 添加新的历史记录
  history.value.push(imageData)
  historyIndex.value = history.value.length - 1
  
  // 限制历史记录数量
  if (history.value.length > 20) {
    history.value.shift()
    historyIndex.value--
  }
}

// 撤销
const undo = () => {
  if (!canUndo.value) return
  
  historyIndex.value--
  const imageData = history.value[historyIndex.value]
  ctx.value.putImageData(imageData, 0, 0)
}

// 重做
const redo = () => {
  if (!canRedo.value) return
  
  historyIndex.value++
  const imageData = history.value[historyIndex.value]
  ctx.value.putImageData(imageData, 0, 0)
}

// 重置
const reset = () => {
  if (!originalImage.value) return
  
  currentImage.value = originalImage.value
  zoomLevel.value = 1
  adjustments.value = {
    brightness: 0,
    contrast: 0,
    saturation: 0,
    hue: 0,
    blur: 0,
    sharpen: 0
  }
  transformations.value = {
    rotation: 0,
    scaleX: 1,
    scaleY: 1,
    scale: 1
  }
  activeFilter.value = 'none'
  
  drawImage()
  saveToHistory()
}

// 初始化裁剪框
const initCropBox = () => {
  const canvasRect = canvas.value.getBoundingClientRect()
  const margin = 50
  
  cropBox.value = {
    x: margin,
    y: margin,
    width: canvasRect.width - margin * 2,
    height: canvasRect.height - margin * 2
  }
  
  showCropBox.value = true
}

// 设置裁剪比例
const setAspectRatio = (ratio) => {
  cropAspectRatio.value = ratio
  
  if (ratio) {
    const currentRatio = cropBox.value.width / cropBox.value.height
    
    if (currentRatio > ratio) {
      cropBox.value.width = cropBox.value.height * ratio
    } else {
      cropBox.value.height = cropBox.value.width / ratio
    }
  }
}

// 更新裁剪尺寸
const updateCropSize = () => {
  if (cropWidth.value > 0 && cropHeight.value > 0) {
    cropBox.value.width = cropWidth.value
    cropBox.value.height = cropHeight.value
    cropAspectRatio.value = cropWidth.value / cropHeight.value
  }
}

// 应用裁剪
const applyCrop = () => {
  if (!ctx.value) return
  
  const imageData = ctx.value.getImageData(
    cropBox.value.x,
    cropBox.value.y,
    cropBox.value.width,
    cropBox.value.height
  )
  
  // 创建新的画布
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  tempCanvas.width = cropBox.value.width
  tempCanvas.height = cropBox.value.height
  
  tempCtx.putImageData(imageData, 0, 0)
  
  // 更新当前图片
  const img = new Image()
  img.onload = () => {
    currentImage.value = img
    canvas.value.width = cropBox.value.width
    canvas.value.height = cropBox.value.height
    drawImage()
    saveToHistory()
    showCropBox.value = false
  }
  img.src = tempCanvas.toDataURL()
}

// 应用调整
const applyAdjustments = () => {
  if (!originalImage.value) return
  
  // 创建临时画布应用滤镜
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  tempCanvas.width = originalImage.value.width
  tempCanvas.height = originalImage.value.height
  
  // 构建CSS滤镜字符串
  const filters = []
  
  if (adjustments.value.brightness !== 0) {
    filters.push(`brightness(${1 + adjustments.value.brightness / 100})`)
  }
  if (adjustments.value.contrast !== 0) {
    filters.push(`contrast(${1 + adjustments.value.contrast / 100})`)
  }
  if (adjustments.value.saturation !== 0) {
    filters.push(`saturate(${1 + adjustments.value.saturation / 100})`)
  }
  if (adjustments.value.hue !== 0) {
    filters.push(`hue-rotate(${adjustments.value.hue}deg)`)
  }
  if (adjustments.value.blur > 0) {
    filters.push(`blur(${adjustments.value.blur}px)`)
  }
  
  tempCtx.filter = filters.join(' ')
  tempCtx.drawImage(originalImage.value, 0, 0)
  
  // 应用锐化（需要手动实现）
  if (adjustments.value.sharpen > 0) {
    applySharpen(tempCtx, tempCanvas.width, tempCanvas.height, adjustments.value.sharpen)
  }
  
  // 更新当前图片
  const img = new Image()
  img.onload = () => {
    currentImage.value = img
    drawImage()
  }
  img.src = tempCanvas.toDataURL()
}

// 应用锐化滤镜
const applySharpen = (ctx, width, height, amount) => {
  const imageData = ctx.getImageData(0, 0, width, height)
  const data = imageData.data
  const factor = amount / 10
  
  // 锐化卷积核
  const kernel = [
    0, -factor, 0,
    -factor, 1 + 4 * factor, -factor,
    0, -factor, 0
  ]
  
  const output = new Uint8ClampedArray(data.length)
  
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      for (let c = 0; c < 3; c++) {
        let sum = 0
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * width + (x + kx)) * 4 + c
            sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)]
          }
        }
        const idx = (y * width + x) * 4 + c
        output[idx] = Math.max(0, Math.min(255, sum))
      }
      // 保持alpha通道
      output[(y * width + x) * 4 + 3] = data[(y * width + x) * 4 + 3]
    }
  }
  
  const newImageData = new ImageData(output, width, height)
  ctx.putImageData(newImageData, 0, 0)
}

// 重置调整
const resetAdjustments = () => {
  adjustments.value = {
    brightness: 0,
    contrast: 0,
    saturation: 0,
    hue: 0,
    blur: 0,
    sharpen: 0
  }
  applyAdjustments()
}

// 应用滤镜
const applyFilter = (filter) => {
  activeFilter.value = filter.name
  
  if (filter.name === 'none') {
    currentImage.value = originalImage.value
    drawImage()
    return
  }
  
  // 创建临时画布应用滤镜
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  tempCanvas.width = originalImage.value.width
  tempCanvas.height = originalImage.value.height
  
  tempCtx.filter = filter.css
  tempCtx.drawImage(originalImage.value, 0, 0)
  
  // 更新当前图片
  const img = new Image()
  img.onload = () => {
    currentImage.value = img
    drawImage()
  }
  img.src = tempCanvas.toDataURL()
}

// 更新滤镜强度
const updateFilterIntensity = () => {
  const filter = filters.find(f => f.name === activeFilter.value)
  if (!filter || filter.name === 'none') return
  
  // 根据强度调整滤镜效果
  const intensity = filterIntensity.value / 100
  let adjustedFilter = filter.css
  
  // 简单的强度调整（实际应用中可能需要更复杂的处理）
  if (filter.name === 'grayscale') {
    adjustedFilter = `grayscale(${intensity * 100}%)`
  } else if (filter.name === 'sepia') {
    adjustedFilter = `sepia(${intensity * 100}%)`
  }
  
  // 重新应用滤镜
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  tempCanvas.width = originalImage.value.width
  tempCanvas.height = originalImage.value.height
  
  tempCtx.filter = adjustedFilter
  tempCtx.drawImage(originalImage.value, 0, 0)
  
  const img = new Image()
  img.onload = () => {
    currentImage.value = img
    drawImage()
  }
  img.src = tempCanvas.toDataURL()
}

// 旋转
const rotate = (angle) => {
  transformations.value.rotation += angle
  applyTransformations()
}

// 翻转
const flip = (direction) => {
  if (direction === 'horizontal') {
    transformations.value.scaleX *= -1
  } else {
    transformations.value.scaleY *= -1
  }
  applyTransformations()
}

// 应用变换
const applyTransformations = () => {
  if (!currentImage.value) return
  
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  tempCanvas.width = currentImage.value.width
  tempCanvas.height = currentImage.value.height
  
  // 设置变换
  tempCtx.save()
  tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2)
  tempCtx.rotate((transformations.value.rotation * Math.PI) / 180)
  tempCtx.scale(
    transformations.value.scaleX * transformations.value.scale,
    transformations.value.scaleY * transformations.value.scale
  )
  
  tempCtx.drawImage(
    currentImage.value,
    -currentImage.value.width / 2,
    -currentImage.value.height / 2
  )
  
  tempCtx.restore()
  
  // 更新显示
  const img = new Image()
  img.onload = () => {
    drawImage()
  }
  img.src = tempCanvas.toDataURL()
}

// 添加文字
const addText = () => {
  if (!textOptions.value.content.trim()) return
  
  ctx.value.save()
  
  // 设置字体样式
  let fontStyle = ''
  if (textOptions.value.italic) fontStyle += 'italic '
  if (textOptions.value.bold) fontStyle += 'bold '
  
  ctx.value.font = `${fontStyle}${textOptions.value.fontSize}px Arial`
  ctx.value.fillStyle = textOptions.value.color
  ctx.value.textAlign = 'center'
  ctx.value.textBaseline = 'middle'
  
  // 在画布中心添加文字
  const x = canvas.value.width / 2
  const y = canvas.value.height / 2
  
  ctx.value.fillText(textOptions.value.content, x, y)
  
  // 如果有下划线，手动绘制
  if (textOptions.value.underline) {
    const metrics = ctx.value.measureText(textOptions.value.content)
    const lineY = y + textOptions.value.fontSize * 0.1
    ctx.value.beginPath()
    ctx.value.moveTo(x - metrics.width / 2, lineY)
    ctx.value.lineTo(x + metrics.width / 2, lineY)
    ctx.value.strokeStyle = textOptions.value.color
    ctx.value.lineWidth = 1
    ctx.value.stroke()
  }
  
  ctx.value.restore()
  saveToHistory()
  
  // 清空文字内容
  textOptions.value.content = ''
}

// 缩放控制
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 5)
  drawImage()
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.1)
  drawImage()
}

const fitToScreen = () => {
  zoomLevel.value = 1
  drawImage()
}

// 保存图片
const saveImage = () => {
  showSaveDialog.value = true
}

const confirmSave = () => {
  const dataURL = canvas.value.toDataURL(
    `image/${saveOptions.value.format}`,
    saveOptions.value.quality
  )
  
  // 创建下载链接
  const link = document.createElement('a')
  link.download = `${saveOptions.value.fileName}.${saveOptions.value.format}`
  link.href = dataURL
  link.click()
  
  // 触发保存事件
  emit('save', {
    dataURL,
    fileName: saveOptions.value.fileName,
    format: saveOptions.value.format
  })
  
  showSaveDialog.value = false
}

// 画布事件处理
const startDrawing = (event) => {
  // 根据当前工具处理不同的绘制逻辑
}

const draw = (event) => {
  // 绘制逻辑
}

const stopDrawing = () => {
  // 停止绘制
}

const handleCanvasClick = (event) => {
  if (activeTool.value === 'text') {
    // 在点击位置添加文字
    const rect = canvas.value.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    if (textOptions.value.content.trim()) {
      ctx.value.save()
      
      let fontStyle = ''
      if (textOptions.value.italic) fontStyle += 'italic '
      if (textOptions.value.bold) fontStyle += 'bold '
      
      ctx.value.font = `${fontStyle}${textOptions.value.fontSize}px Arial`
      ctx.value.fillStyle = textOptions.value.color
      ctx.value.textAlign = 'left'
      ctx.value.textBaseline = 'top'
      
      ctx.value.fillText(textOptions.value.content, x, y)
      
      if (textOptions.value.underline) {
        const metrics = ctx.value.measureText(textOptions.value.content)
        const lineY = y + textOptions.value.fontSize * 1.1
        ctx.value.beginPath()
        ctx.value.moveTo(x, lineY)
        ctx.value.lineTo(x + metrics.width, lineY)
        ctx.value.strokeStyle = textOptions.value.color
        ctx.value.lineWidth = 1
        ctx.value.stroke()
      }
      
      ctx.value.restore()
      saveToHistory()
      textOptions.value.content = ''
    }
  }
}

// 裁剪框拖拽
const startCropResize = (event, handle) => {
  event.preventDefault()
  
  const startX = event.clientX
  const startY = event.clientY
  const startBox = { ...cropBox.value }
  
  const handleMouseMove = (e) => {
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY
    
    // 根据拖拽手柄更新裁剪框
    switch (handle) {
      case 'nw':
        cropBox.value.x = startBox.x + deltaX
        cropBox.value.y = startBox.y + deltaY
        cropBox.value.width = startBox.width - deltaX
        cropBox.value.height = startBox.height - deltaY
        break
      case 'n':
        cropBox.value.y = startBox.y + deltaY
        cropBox.value.height = startBox.height - deltaY
        break
      case 'ne':
        cropBox.value.y = startBox.y + deltaY
        cropBox.value.width = startBox.width + deltaX
        cropBox.value.height = startBox.height - deltaY
        break
      case 'w':
        cropBox.value.x = startBox.x + deltaX
        cropBox.value.width = startBox.width - deltaX
        break
      case 'e':
        cropBox.value.width = startBox.width + deltaX
        break
      case 'sw':
        cropBox.value.x = startBox.x + deltaX
        cropBox.value.width = startBox.width - deltaX
        cropBox.value.height = startBox.height + deltaY
        break
      case 's':
        cropBox.value.height = startBox.height + deltaY
        break
      case 'se':
        cropBox.value.width = startBox.width + deltaX
        cropBox.value.height = startBox.height + deltaY
        break
    }
    
    // 保持最小尺寸
    cropBox.value.width = Math.max(50, cropBox.value.width)
    cropBox.value.height = Math.max(50, cropBox.value.height)
    
    // 保持纵横比
    if (cropAspectRatio.value) {
      if (handle.includes('e') || handle.includes('w')) {
        cropBox.value.height = cropBox.value.width / cropAspectRatio.value
      } else if (handle.includes('n') || handle.includes('s')) {
        cropBox.value.width = cropBox.value.height * cropAspectRatio.value
      }
    }
    
    // 更新输入框值
    cropWidth.value = Math.round(cropBox.value.width)
    cropHeight.value = Math.round(cropBox.value.height)
  }
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

onUnmounted(() => {
  // 清理事件监听器
})
</script>

<style scoped>
.image-editor {
  @apply flex flex-col h-full bg-white;
}

.editor-toolbar {
  @apply flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200;
}

.toolbar-section {
  @apply flex items-center space-x-4;
}

.toolbar-title {
  @apply text-lg font-semibold text-gray-900;
}

.toolbar-actions {
  @apply flex items-center space-x-2;
}

.tool-btn {
  @apply flex flex-col items-center p-3 rounded-lg hover:bg-gray-100 transition-colors;
}

.tool-btn-active {
  @apply bg-blue-100 text-blue-600;
}

.tool-label {
  @apply text-xs mt-1;
}

.toolbar-controls {
  @apply flex items-center space-x-2;
}

.control-btn {
  @apply p-2 rounded hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.control-btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.editor-workspace {
  @apply flex flex-1 overflow-hidden;
}

.editor-sidebar {
  @apply w-80 bg-gray-50 border-r border-gray-200 overflow-y-auto;
}

.tool-panel {
  @apply p-4;
}

.panel-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

.panel-content {
  @apply space-y-4;
}

.control-group {
  @apply space-y-2;
}

.control-label {
  @apply block text-sm font-medium text-gray-700;
}

.aspect-ratio-grid {
  @apply grid grid-cols-3 gap-2;
}

.aspect-btn {
  @apply px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.aspect-btn-active {
  @apply border-blue-500 bg-blue-50 text-blue-600;
}

.size-inputs {
  @apply flex items-center space-x-2;
}

.size-input {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.size-separator {
  @apply text-gray-500;
}

.apply-btn {
  @apply w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors;
}

.slider-container {
  @apply flex items-center space-x-3;
}

.slider {
  @apply flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
}

.slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-blue-600 rounded-full cursor-pointer;
}

.slider-value {
  @apply text-sm text-gray-600 min-w-[3rem] text-right;
}

.reset-btn {
  @apply w-full px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors;
}

.filter-grid {
  @apply grid grid-cols-2 gap-3;
}

.filter-item {
  @apply flex flex-col items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors;
}

.filter-item-active {
  @apply border-blue-500 bg-blue-50;
}

.filter-preview {
  @apply w-12 h-12 mb-2 rounded;
}

.filter-sample {
  @apply w-full h-full bg-gradient-to-br from-red-400 via-yellow-400 to-blue-400 rounded;
}

.filter-name {
  @apply text-xs text-center;
}

.transform-buttons {
  @apply flex space-x-2 mb-3;
}

.transform-btn {
  @apply flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.text-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none;
}

.color-input {
  @apply w-full h-10 border border-gray-300 rounded cursor-pointer;
}

.font-options {
  @apply flex space-x-2;
}

.font-btn {
  @apply p-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.font-btn-active {
  @apply border-blue-500 bg-blue-50 text-blue-600;
}

.editor-canvas-container {
  @apply flex-1 flex flex-col relative;
}

.canvas-wrapper {
  @apply flex-1 flex items-center justify-center bg-gray-100 relative overflow-hidden;
}

.canvas-wrapper canvas {
  @apply max-w-full max-h-full border border-gray-300 bg-white;
}

.crop-box {
  @apply absolute border-2 border-blue-500 bg-blue-500 bg-opacity-10;
}

.crop-handles {
  @apply relative w-full h-full;
}

.crop-handle {
  @apply absolute w-3 h-3 bg-blue-500 border border-white;
}

.crop-handle-nw { @apply -top-1 -left-1 cursor-nw-resize; }
.crop-handle-n { @apply -top-1 left-1/2 -translate-x-1/2 cursor-n-resize; }
.crop-handle-ne { @apply -top-1 -right-1 cursor-ne-resize; }
.crop-handle-w { @apply top-1/2 -left-1 -translate-y-1/2 cursor-w-resize; }
.crop-handle-e { @apply top-1/2 -right-1 -translate-y-1/2 cursor-e-resize; }
.crop-handle-sw { @apply -bottom-1 -left-1 cursor-sw-resize; }
.crop-handle-s { @apply -bottom-1 left-1/2 -translate-x-1/2 cursor-s-resize; }
.crop-handle-se { @apply -bottom-1 -right-1 cursor-se-resize; }

.zoom-controls {
  @apply flex items-center justify-center space-x-4 p-4 bg-gray-50 border-t border-gray-200;
}

.zoom-btn {
  @apply p-2 border border-gray-300 rounded hover:bg-gray-100 transition-colors;
}

.zoom-level {
  @apply text-sm font-medium text-gray-700;
}
</style>
