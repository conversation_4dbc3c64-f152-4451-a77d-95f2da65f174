<template>
  <div class="file-upload-container">
    <!-- 拖拽上传区域 -->
    <div
      ref="dropZone"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      :class="[
        'upload-zone',
        isDragging ? 'upload-zone-active' : '',
        disabled ? 'upload-zone-disabled' : ''
      ]"
    >
      <input
        ref="fileInput"
        type="file"
        :multiple="multiple"
        :accept="accept"
        @change="handleFileSelect"
        class="hidden"
        :disabled="disabled"
      />
      
      <div class="upload-content">
        <div v-if="!uploading" class="upload-prompt">
          <UploadIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p class="text-lg font-medium text-gray-700 mb-2">
            {{ isDragging ? '释放文件以上传' : '拖拽文件到此处或点击上传' }}
          </p>
          <p class="text-sm text-gray-500 mb-4">
            支持 {{ acceptText }}，单个文件最大 {{ maxSizeText }}
          </p>
          <button
            @click="selectFiles"
            :disabled="disabled"
            class="btn btn-primary"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            选择文件
          </button>
        </div>
        
        <div v-else class="upload-progress">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-lg font-medium text-gray-700 mb-2">正在上传...</p>
          <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
          <p class="text-sm text-gray-500">{{ uploadProgress }}% 完成</p>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="files.length > 0" class="file-list mt-6">
      <h4 class="font-medium text-gray-900 mb-4">已选择的文件 ({{ files.length }})</h4>
      <div class="space-y-3">
        <div
          v-for="(file, index) in files"
          :key="file.id || index"
          class="file-item"
        >
          <div class="flex items-center space-x-4">
            <!-- 文件预览 -->
            <div class="file-preview">
              <img
                v-if="file.type && file.type.startsWith('image/') && file.preview"
                :src="file.preview"
                :alt="file.name"
                class="w-12 h-12 object-cover rounded"
              />
              <div v-else class="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                <FileIcon class="h-6 w-6 text-gray-400" />
              </div>
            </div>
            
            <!-- 文件信息 -->
            <div class="flex-1 min-w-0">
              <p class="font-medium text-gray-900 truncate">{{ file.name }}</p>
              <div class="flex items-center space-x-4 text-sm text-gray-500">
                <span>{{ formatFileSize(file.size) }}</span>
                <span>{{ getFileType(file.type || file.name) }}</span>
                <span v-if="file.status" :class="getStatusClass(file.status)">
                  {{ getStatusText(file.status) }}
                </span>
              </div>
              
              <!-- 上传进度 -->
              <div v-if="file.status === 'uploading'" class="mt-2">
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div
                    class="bg-blue-600 h-1 rounded-full transition-all duration-300"
                    :style="{ width: `${file.progress || 0}%` }"
                  ></div>
                </div>
              </div>
              
              <!-- 错误信息 -->
              <div v-if="file.status === 'error' && file.error" class="mt-1">
                <p class="text-sm text-red-600">{{ file.error }}</p>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-center space-x-2">
              <button
                v-if="file.type && file.type.startsWith('image/') && file.status === 'success'"
                @click="editImage(file, index)"
                class="btn btn-ghost btn-sm"
                title="编辑图片"
              >
                <EditIcon class="h-4 w-4" />
              </button>
              <button
                v-if="file.status === 'success' && file.url"
                @click="previewFile(file)"
                class="btn btn-ghost btn-sm"
                title="预览"
              >
                <EyeIcon class="h-4 w-4" />
              </button>
              <button
                v-if="file.status === 'success' && file.url"
                @click="copyFileUrl(file)"
                class="btn btn-ghost btn-sm"
                title="复制链接"
              >
                <CopyIcon class="h-4 w-4" />
              </button>
              <button
                @click="removeFile(index)"
                class="btn btn-ghost btn-sm text-red-600"
                title="删除"
              >
                <XIcon class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 批量操作 -->
      <div v-if="files.length > 1" class="flex items-center justify-between mt-4 pt-4 border-t">
        <div class="text-sm text-gray-600">
          共 {{ files.length }} 个文件，总大小 {{ formatFileSize(totalSize) }}
        </div>
        <div class="flex items-center space-x-2">
          <button
            v-if="hasUploadableFiles"
            @click="uploadAll"
            :disabled="uploading"
            class="btn btn-primary btn-sm"
          >
            {{ uploading ? '上传中...' : '全部上传' }}
          </button>
          <button
            @click="clearAll"
            class="btn btn-outline btn-sm"
          >
            清空列表
          </button>
        </div>
      </div>
    </div>

    <!-- 上传结果 -->
    <div v-if="uploadResults.length > 0" class="upload-results mt-6">
      <h4 class="font-medium text-gray-900 mb-4">上传结果</h4>
      <div class="space-y-2">
        <div
          v-for="result in uploadResults"
          :key="result.id"
          :class="[
            'p-3 rounded-lg text-sm',
            result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
          ]"
        >
          <div class="flex items-center justify-between">
            <span>{{ result.fileName }}</span>
            <span v-if="result.success" class="text-green-600">上传成功</span>
            <span v-else class="text-red-600">上传失败</span>
          </div>
          <div v-if="result.error" class="mt-1 text-xs">
            {{ result.error }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图片编辑模态框 -->
    <ImageEditModal
      :show="showImageEditor"
      :image-url="editingImage?.url || editingImage?.preview"
      :file-name="editingImage?.name"
      @close="closeImageEditor"
      @save="handleImageEditSave"
      @apply-preset="handlePresetApply"
      @reset-to-original="handleResetToOriginal"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { fileAPI } from '../services/api.js'
import {
  UploadIcon,
  PlusIcon,
  FileIcon,
  EyeIcon,
  CopyIcon,
  XIcon,
  EditIcon
} from 'lucide-vue-next'
import ImageEditModal from './ImageEditModal.vue'

// 添加图片编辑相关状态
const showImageEditor = ref(false)
const editingImage = ref(null)
const editingImageIndex = ref(-1)

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt'
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  maxFiles: {
    type: Number,
    default: 10
  },
  disabled: {
    type: Boolean,
    default: false
  },
  autoUpload: {
    type: Boolean,
    default: false
  },
  uploadPath: {
    type: String,
    default: 'uploads'
  }
})

const emit = defineEmits(['upload-success', 'upload-error', 'files-change'])

// 响应式数据
const dropZone = ref(null)
const fileInput = ref(null)
const isDragging = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const files = ref([])
const uploadResults = ref([])

// 计算属性
const acceptText = computed(() => {
  const types = props.accept.split(',').map(type => type.trim())
  return types.join(', ')
})

const maxSizeText = computed(() => {
  return formatFileSize(props.maxSize)
})

const totalSize = computed(() => {
  return files.value.reduce((total, file) => total + (file.size || 0), 0)
})

const hasUploadableFiles = computed(() => {
  return files.value.some(file => !file.status || file.status === 'pending')
})

// 监听文件变化
watch(files, (newFiles) => {
  emit('files-change', newFiles)
}, { deep: true })

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件类型
const getFileType = (type) => {
  if (type) {
    if (type.startsWith('image/')) return '图片'
    if (type.startsWith('video/')) return '视频'
    if (type.startsWith('audio/')) return '音频'
    if (type.includes('pdf')) return 'PDF'
    if (type.includes('word') || type.includes('document')) return '文档'
    if (type.includes('text')) return '文本'
  }
  return '文件'
}

// 获取状态样式
const getStatusClass = (status) => {
  const classes = {
    pending: 'text-gray-500',
    uploading: 'text-blue-600',
    success: 'text-green-600',
    error: 'text-red-600'
  }
  return classes[status] || 'text-gray-500'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '等待上传',
    uploading: '上传中',
    success: '上传成功',
    error: '上传失败'
  }
  return texts[status] || '未知状态'
}

// 验证文件
const validateFile = (file) => {
  const errors = []
  
  // 检查文件大小
  if (file.size > props.maxSize) {
    errors.push(`文件大小超过限制 (${formatFileSize(props.maxSize)})`)
  }
  
  // 检查文件类型
  if (props.accept && props.accept !== '*') {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const isAccepted = acceptTypes.some(acceptType => {
      if (acceptType.startsWith('.')) {
        return file.name.toLowerCase().endsWith(acceptType.toLowerCase())
      } else if (acceptType.includes('*')) {
        const baseType = acceptType.split('/')[0]
        return file.type.startsWith(baseType)
      } else {
        return file.type === acceptType
      }
    })
    
    if (!isAccepted) {
      errors.push('不支持的文件类型')
    }
  }
  
  return errors
}

// 创建文件预览
const createFilePreview = (file) => {
  return new Promise((resolve) => {
    if (file.type && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = () => resolve(null)
      reader.readAsDataURL(file)
    } else {
      resolve(null)
    }
  })
}

// 添加文件
const addFiles = async (fileList) => {
  const newFiles = Array.from(fileList)
  
  // 检查文件数量限制
  if (files.value.length + newFiles.length > props.maxFiles) {
    alert(`最多只能上传 ${props.maxFiles} 个文件`)
    return
  }
  
  for (const file of newFiles) {
    const errors = validateFile(file)
    const preview = await createFilePreview(file)
    
    const fileObj = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file,
      preview: preview,
      status: errors.length > 0 ? 'error' : 'pending',
      error: errors.join(', '),
      progress: 0,
      url: null
    }
    
    files.value.push(fileObj)
  }
  
  // 自动上传
  if (props.autoUpload) {
    uploadAll()
  }
}

// 选择文件
const selectFiles = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// 处理文件选择
const handleFileSelect = (event) => {
  const fileList = event.target.files
  if (fileList && fileList.length > 0) {
    addFiles(fileList)
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = ''
}

// 拖拽处理
const handleDragEnter = (e) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragOver = (e) => {
  e.preventDefault()
}

const handleDragLeave = (e) => {
  e.preventDefault()
  if (!dropZone.value.contains(e.relatedTarget)) {
    isDragging.value = false
  }
}

const handleDrop = (e) => {
  e.preventDefault()
  isDragging.value = false
  
  if (props.disabled) return
  
  const fileList = e.dataTransfer.files
  if (fileList && fileList.length > 0) {
    addFiles(fileList)
  }
}

// 上传单个文件
const uploadFile = async (fileObj) => {
  if (fileObj.status === 'success' || fileObj.status === 'uploading') {
    return
  }
  
  try {
    fileObj.status = 'uploading'
    fileObj.progress = 0
    
    const formData = new FormData()
    formData.append('file', fileObj.file)
    formData.append('path', props.uploadPath)
    
    const response = await fileAPI.uploadFile(formData, {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.lengthComputable) {
          fileObj.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    })
    
    if (response.code === 0 && response.data) {
      fileObj.status = 'success'
      fileObj.url = response.data.url
      fileObj.cdnUrl = response.data.cdnUrl
      fileObj.progress = 100
      
      uploadResults.value.push({
        id: fileObj.id,
        fileName: fileObj.name,
        success: true,
        url: fileObj.url
      })
      
      emit('upload-success', {
        file: fileObj,
        url: fileObj.url,
        cdnUrl: fileObj.cdnUrl
      })
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    fileObj.status = 'error'
    fileObj.error = error.message || '上传失败'
    fileObj.progress = 0
    
    uploadResults.value.push({
      id: fileObj.id,
      fileName: fileObj.name,
      success: false,
      error: error.message
    })
    
    emit('upload-error', {
      file: fileObj,
      error: error.message
    })
  }
}

// 上传所有文件
const uploadAll = async () => {
  const uploadableFiles = files.value.filter(file => 
    file.status === 'pending' || file.status === 'error'
  )
  
  if (uploadableFiles.length === 0) return
  
  uploading.value = true
  uploadProgress.value = 0
  
  try {
    const promises = uploadableFiles.map(file => uploadFile(file))
    await Promise.all(promises)
  } finally {
    uploading.value = false
    uploadProgress.value = 100
  }
}

// 移除文件
const removeFile = (index) => {
  files.value.splice(index, 1)
}

// 清空所有文件
const clearAll = () => {
  files.value = []
  uploadResults.value = []
}

// 预览文件
const previewFile = (file) => {
  if (file.url) {
    window.open(file.cdnUrl || file.url, '_blank')
  }
}

// 复制文件链接
const copyFileUrl = async (file) => {
  if (!file.url) return
  
  try {
    await navigator.clipboard.writeText(file.cdnUrl || file.url)
    alert('链接已复制到剪贴板')
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = file.cdnUrl || file.url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert('链接已复制到剪贴板')
  }
}

// 编辑图片
const editImage = (file, index) => {
  editingImage.value = file
  editingImageIndex.value = index
  showImageEditor.value = true
}

// 关闭图片编辑器
const closeImageEditor = () => {
  showImageEditor.value = false
  editingImage.value = null
  editingImageIndex.value = -1
}

// 处理图片编辑保存
const handleImageEditSave = async (imageData) => {
  if (editingImageIndex.value >= 0) {
    try {
      // 将编辑后的图片数据转换为文件并重新上传
      const response = await fetch(imageData.dataURL)
      const blob = await response.blob()
      const file = new File([blob], imageData.fileName, { type: `image/${imageData.format}` })
      
      // 更新文件列表中的文件
      const fileObj = files.value[editingImageIndex.value]
      fileObj.file = file
      fileObj.name = imageData.fileName
      fileObj.preview = imageData.dataURL
      fileObj.status = 'pending' // 重新设置为待上传状态
      fileObj.error = null

      // 重新上传编辑后的图片
      await uploadFile(fileObj)
    } catch (error) {
      console.error('图片编辑保存失败', error)
      alert('图片编辑保存失败')
    } finally {
      closeImageEditor()
    }
  }
}

// 应用预设
const handlePresetApply = (preset) => {
  // TODO: 应用预设
  console.log('应用预设', preset)
}

// 重置为原始图片
const handleResetToOriginal = () => {
  // TODO: 重置为原始图片
  console.log('重置为原始图片')
}

// 暴露方法给父组件
defineExpose({
  uploadAll,
  clearAll,
  getFiles: () => files.value,
  getSuccessFiles: () => files.value.filter(f => f.status === 'success')
})
</script>

<style scoped>
.upload-zone {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-colors;
}

.upload-zone:hover {
  @apply border-gray-400 bg-gray-50;
}

.upload-zone-active {
  @apply border-blue-500 bg-blue-50;
}

.upload-zone-disabled {
  @apply border-gray-200 bg-gray-100 cursor-not-allowed;
}

.file-item {
  @apply p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors;
}

.upload-results {
  @apply border-t pt-4;
}
</style>
