<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">内容管理</h1>
        <p class="text-gray-600">管理系统中的所有文章和动态</p>
      </div>
      <div class="flex items-center space-x-4">
        <select v-model="contentType" @change="loadContent" class="input">
          <option value="">全部内容</option>
          <option value="ARTICLE">文章</option>
          <option value="DYNAMIC">动态</option>
        </select>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="card-content p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">搜索内容</label>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="标题、内容..."
              class="input"
              @input="debouncedSearch"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">审核状态</label>
            <select v-model="statusFilter" @change="loadContent" class="input">
              <option value="">全部状态</option>
              <option value="PENDING">待审核</option>
              <option value="APPROVED">已通过</option>
              <option value="REJECTED">已拒绝</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">分类</label>
            <select v-model="categoryFilter" @change="loadContent" class="input">
              <option value="">全部分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">发布时间</label>
            <select v-model="timeFilter" @change="loadContent" class="input">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedItems.length > 0" class="card">
      <div class="card-content p-4">
        <div class="flex items-center justify-between">
          <p class="text-sm text-gray-600">已选择 {{ selectedItems.length }} 项</p>
          <div class="flex items-center space-x-2">
            <button @click="batchApprove" class="btn btn-success btn-sm">
              批量通过
            </button>
            <button @click="batchReject" class="btn btn-warning btn-sm">
              批量拒绝
            </button>
            <button @click="batchDelete" class="btn btn-error btn-sm">
              批量删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容列表 -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-4 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="content.length === 0" class="text-center py-12">
          <FileTextIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-600">没有找到内容</p>
        </div>

        <div v-else class="space-y-4">
          <div v-for="item in content" :key="item.id" class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
            <div class="flex items-start space-x-4">
              <input
                type="checkbox"
                :value="item.id"
                v-model="selectedItems"
                class="mt-1"
              />
              
              <div class="flex-1 space-y-3">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <span :class="[
                        'badge text-xs',
                        item.type === 'ARTICLE' ? 'badge-primary' : 'badge-secondary'
                      ]">
                        {{ item.type === 'ARTICLE' ? '文章' : '动态' }}
                      </span>
                      <span v-if="item.category" class="badge badge-outline text-xs">
                        {{ item.category.name || item.category }}
                      </span>
                      <span :class="[
                        'badge text-xs',
                        item.status === 'APPROVED' ? 'badge-success' : 
                        item.status === 'PENDING' ? 'badge-warning' : 'badge-error'
                      ]">
                        {{ getStatusText(item.status) }}
                      </span>
                    </div>
                    
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                      {{ item.title || item.content.substring(0, 50) + '...' }}
                    </h3>
                    
                    <p class="text-gray-600 text-sm line-clamp-2">
                      {{ item.content.substring(0, 200) }}{{ item.content.length > 200 ? '...' : '' }}
                    </p>
                    
                    <div class="flex items-center space-x-4 text-sm text-gray-500 mt-3">
                      <div class="flex items-center space-x-2">
                        <div class="avatar w-6 h-6">
                          <img :src="item.author?.avatar || '/placeholder.svg?height=24&width=24'" :alt="item.author?.name" />
                        </div>
                        <span>{{ item.author?.name || '匿名用户' }}</span>
                      </div>
                      <span>{{ formatDate(item.createdAt) }}</span>
                      <span>{{ item.viewCount || 0 }} 浏览</span>
                      <span>{{ item.likeCount || 0 }} 点赞</span>
                      <span>{{ item.commentCount || 0 }} 评论</span>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <button
                      @click="viewContent(item)"
                      class="btn btn-ghost btn-sm"
                      title="查看详情"
                    >
                      <EyeIcon class="h-4 w-4" />
                    </button>
                    <button
                      v-if="item.status === 'PENDING'"
                      @click="approveContent(item)"
                      class="btn btn-success btn-sm"
                      title="通过审核"
                    >
                      <CheckIcon class="h-4 w-4" />
                    </button>
                    <button
                      v-if="item.status === 'PENDING'"
                      @click="rejectContent(item)"
                      class="btn btn-warning btn-sm"
                      title="拒绝审核"
                    >
                      <XIcon class="h-4 w-4" />
                    </button>
                    <button
                      @click="editContent(item)"
                      class="btn btn-ghost btn-sm"
                      title="编辑"
                    >
                      <EditIcon class="h-4 w-4" />
                    </button>
                    <button
                      @click="deleteContent(item)"
                      class="btn btn-error btn-sm"
                      title="删除"
                    >
                      <TrashIcon class="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <!-- 标签 -->
                <div v-if="item.tags && item.tags.length > 0" class="flex flex-wrap gap-1">
                  <span v-for="tag in item.tags" :key="tag" class="badge badge-outline text-xs">
                    #{{ tag }}
                  </span>
                </div>
                
                <!-- 图片预览 -->
                <div v-if="item.imageUrls && item.imageUrls.length > 0" class="grid grid-cols-4 gap-2">
                  <img
                    v-for="(img, idx) in item.imageUrls.slice(0, 4)"
                    :key="idx"
                    :src="img"
                    alt=""
                    class="w-16 h-16 object-cover rounded"
                  />
                  <div v-if="item.imageUrls.length > 4" class="w-16 h-16 bg-gray-100 rounded flex items-center justify-center text-xs text-gray-500">
                    +{{ item.imageUrls.length - 4 }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between mt-6 px-6 pb-6">
          <p class="text-sm text-gray-700">
            显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalContent) }} 条，
            共 {{ totalContent }} 条记录
          </p>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="btn btn-outline btn-sm"
            >
              上一页
            </button>
            <span class="text-sm">第 {{ currentPage }} / {{ totalPages }} 页</span>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="btn btn-outline btn-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容详情模态框 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">内容详情</h3>
          <button @click="showDetailModal = false" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
        
        <div v-if="selectedContent" class="space-y-4">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium">类型：</span>
              {{ selectedContent.type === 'ARTICLE' ? '文章' : '动态' }}
            </div>
            <div>
              <span class="font-medium">状态：</span>
              {{ getStatusText(selectedContent.status) }}
            </div>
            <div>
              <span class="font-medium">作者：</span>
              {{ selectedContent.author?.name || '匿名用户' }}
            </div>
            <div>
              <span class="font-medium">发布时间：</span>
              {{ formatDate(selectedContent.createdAt) }}
            </div>
          </div>
          
          <div v-if="selectedContent.title">
            <h4 class="font-medium mb-2">标题</h4>
            <p class="text-gray-800">{{ selectedContent.title }}</p>
          </div>
          
          <div>
            <h4 class="font-medium mb-2">内容</h4>
            <div class="prose max-w-none">
              <div class="whitespace-pre-wrap text-gray-800" v-html="formatContent(selectedContent.content)"></div>
            </div>
          </div>
          
          <div v-if="selectedContent.imageUrls && selectedContent.imageUrls.length > 0">
            <h4 class="font-medium mb-2">图片</h4>
            <div class="grid grid-cols-3 gap-2">
              <img
                v-for="(img, idx) in selectedContent.imageUrls"
                :key="idx"
                :src="img"
                alt=""
                class="w-full h-32 object-cover rounded"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../../services/api.js'
import { 
  FileTextIcon,
  EyeIcon,
  CheckIcon,
  XIcon,
  EditIcon,
  TrashIcon
} from 'lucide-vue-next'

// 响应式数据
const content = ref([])
const categories = ref([])
const loading = ref(false)
const selectedItems = ref([])

// 搜索和筛选
const searchQuery = ref('')
const contentType = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const timeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalContent = ref(0)
const totalPages = ref(0)

// 模态框
const showDetailModal = ref(false)
const selectedContent = ref(null)

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadContent()
  }, 500)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 格式化内容
const formatContent = (content) => {
  if (!content) return ''
  return content.replace(/\n/g, '<br>')
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审核',
    'APPROVED': '已通过',
    'REJECTED': '已拒绝'
  }
  return statusMap[status] || status
}

// 加载内容列表
const loadContent = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      search: searchQuery.value,
      type: contentType.value,
      status: statusFilter.value,
      categoryId: categoryFilter.value,
      timeRange: timeFilter.value
    }
    
    const response = await adminAPI.getContent(params)
    
    if (response.code === 0 && response.data) {
      if (Array.isArray(response.data)) {
        content.value = response.data
        totalContent.value = response.data.length
        totalPages.value = 1
      } else {
        content.value = response.data.content || []
        totalContent.value = response.data.totalElements || 0
        totalPages.value = response.data.totalPages || 0
      }
    }
  } catch (err) {
    console.error('Failed to load content:', err)
  } finally {
    loading.value = false
  }
}

// 加载分类
const loadCategories = async () => {
  try {
    const response = await adminAPI.getCategories()
    
    if (response.code === 0 && response.data) {
      categories.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (err) {
    console.error('Failed to load categories:', err)
  }
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadContent()
  }
}

// 查看内容详情
const viewContent = (item) => {
  selectedContent.value = item
  showDetailModal.value = true
}

// 通过审核
const approveContent = async (item) => {
  try {
    const response = await adminAPI.approveContent(item.id)
    
    if (response.code === 0) {
      item.status = 'APPROVED'
    } else {
      throw new Error(response.message || '审核失败')
    }
  } catch (err) {
    console.error('Failed to approve content:', err)
    alert(err.message || '审核失败')
  }
}

// 拒绝审核
const rejectContent = async (item) => {
  const reason = prompt('请输入拒绝原因：')
  if (!reason) return

  try {
    const response = await adminAPI.rejectContent(item.id, reason)
    
    if (response.code === 0) {
      item.status = 'REJECTED'
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (err) {
    console.error('Failed to reject content:', err)
    alert(err.message || '操作失败')
  }
}

// 编辑内容
const editContent = (item) => {
  // 这里可以跳转到编辑页面或打开编辑模态框
  console.log('Edit content:', item)
}

// 删除内容
const deleteContent = async (item) => {
  if (!confirm(`确定要删除这个${item.type === 'ARTICLE' ? '文章' : '动态'}吗？`)) return

  try {
    const response = await adminAPI.deleteContent(item.id)
    
    if (response.code === 0) {
      loadContent()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (err) {
    console.error('Failed to delete content:', err)
    alert(err.message || '删除失败')
  }
}

// 批量通过
const batchApprove = async () => {
  if (!confirm(`确定要批量通过 ${selectedItems.value.length} 个内容吗？`)) return

  try {
    const response = await adminAPI.batchApproveContent(selectedItems.value)
    
    if (response.code === 0) {
      selectedItems.value = []
      loadContent()
    } else {
      throw new Error(response.message || '批量操作失败')
    }
  } catch (err) {
    console.error('Failed to batch approve:', err)
    alert(err.message || '批量操作失败')
  }
}

// 批量拒绝
const batchReject = async () => {
  const reason = prompt('请输入拒绝原因：')
  if (!reason) return

  try {
    const response = await adminAPI.batchRejectContent(selectedItems.value, reason)
    
    if (response.code === 0) {
      selectedItems.value = []
      loadContent()
    } else {
      throw new Error(response.message || '批量操作失败')
    }
  } catch (err) {
    console.error('Failed to batch reject:', err)
    alert(err.message || '批量操作失败')
  }
}

// 批量删除
const batchDelete = async () => {
  if (!confirm(`确定要批量删除 ${selectedItems.value.length} 个内容吗？此操作不可恢复！`)) return

  try {
    const response = await adminAPI.batchDeleteContent(selectedItems.value)
    
    if (response.code === 0) {
      selectedItems.value = []
      loadContent()
    } else {
      throw new Error(response.message || '批量操作失败')
    }
  } catch (err) {
    console.error('Failed to batch delete:', err)
    alert(err.message || '批量操作失败')
  }
}

onMounted(() => {
  loadContent()
  loadCategories()
})
</script>
