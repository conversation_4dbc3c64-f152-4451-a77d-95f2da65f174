<template>
  <div class="file-manager">
    <!-- 工具栏 -->
    <div class="toolbar mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h3 class="text-lg font-semibold">文件管理</h3>
          <div class="flex items-center space-x-2">
            <button
              v-for="view in viewModes"
              :key="view.mode"
              @click="currentView = view.mode"
              :class="[
                'btn btn-sm',
                currentView === view.mode ? 'btn-primary' : 'btn-outline'
              ]"
            >
              <component :is="view.icon" class="h-4 w-4" />
            </button>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <select v-model="sortBy" @change="loadFiles" class="input input-sm">
              <option value="createdAt">按时间排序</option>
              <option value="name">按名称排序</option>
              <option value="size">按大小排序</option>
              <option value="type">按类型排序</option>
            </select>
            <button
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'; loadFiles()"
              class="btn btn-outline btn-sm"
            >
              <ArrowUpDownIcon class="h-4 w-4" />
            </button>
          </div>
          
          <button @click="showUploadModal = true" class="btn btn-primary btn-sm">
            <UploadIcon class="h-4 w-4 mr-2" />
            上传文件
          </button>
          
          <button
            v-if="selectedFiles.length > 0"
            @click="deleteSelectedFiles"
            class="btn btn-error btn-sm"
          >
            <TrashIcon class="h-4 w-4 mr-2" />
            删除选中 ({{ selectedFiles.length }})
          </button>
        </div>
      </div>
      
      <!-- 搜索和筛选 -->
      <div class="flex items-center space-x-4 mt-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索文件名..."
            class="input"
            @input="debouncedSearch"
          />
        </div>
        <select v-model="typeFilter" @change="loadFiles" class="input">
          <option value="">全部类型</option>
          <option value="image">图片</option>
          <option value="video">视频</option>
          <option value="audio">音频</option>
          <option value="document">文档</option>
          <option value="other">其他</option>
        </select>
        <select v-model="sizeFilter" @change="loadFiles" class="input">
          <option value="">全部大小</option>
          <option value="small">小于1MB</option>
          <option value="medium">1MB-10MB</option>
          <option value="large">大于10MB</option>
        </select>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p class="mt-4 text-gray-600">加载中...</p>
    </div>

    <div v-else-if="files.length === 0" class="text-center py-12">
      <FolderIcon class="h-16 w-16 mx-auto mb-4 text-gray-400" />
      <p class="text-gray-600 mb-4">没有找到文件</p>
      <button @click="showUploadModal = true" class="btn btn-primary">
        上传第一个文件
      </button>
    </div>

    <!-- 网格视图 -->
    <div v-else-if="currentView === 'grid'" class="file-grid">
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <div
          v-for="file in files"
          :key="file.id"
          @click="selectFile(file)"
          :class="[
            'file-card',
            selectedFiles.includes(file.id) ? 'file-card-selected' : ''
          ]"
        >
          <div class="file-preview">
            <img
              v-if="file.type.startsWith('image/')"
              :src="file.thumbnailUrl || file.cdnUrl || file.url"
              :alt="file.name"
              class="w-full h-24 object-cover rounded"
            />
            <div v-else class="w-full h-24 bg-gray-100 rounded flex items-center justify-center">
              <component :is="getFileIcon(file.type)" class="h-8 w-8 text-gray-400" />
            </div>
          </div>
          
          <div class="file-info mt-2">
            <p class="text-sm font-medium truncate" :title="file.name">{{ file.name }}</p>
            <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
          </div>
          
          <div class="file-actions">
            <button
              @click.stop="previewFile(file)"
              class="btn btn-ghost btn-xs"
              title="预览"
            >
              <EyeIcon class="h-3 w-3" />
            </button>
            <button
              @click.stop="downloadFile(file)"
              class="btn btn-ghost btn-xs"
              title="下载"
            >
              <DownloadIcon class="h-3 w-3" />
            </button>
            <button
              @click.stop="deleteFile(file)"
              class="btn btn-ghost btn-xs text-red-600"
              title="删除"
            >
              <TrashIcon class="h-3 w-3" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-else class="file-list">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-3 px-4">
                <input
                  type="checkbox"
                  :checked="allSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th class="text-left py-3 px-4 font-medium">名称</th>
              <th class="text-left py-3 px-4 font-medium">类型</th>
              <th class="text-left py-3 px-4 font-medium">大小</th>
              <th class="text-left py-3 px-4 font-medium">上传时间</th>
              <th class="text-left py-3 px-4 font-medium">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="file in files"
              :key="file.id"
              class="border-b border-gray-100 hover:bg-gray-50"
            >
              <td class="py-3 px-4">
                <input
                  type="checkbox"
                  :value="file.id"
                  v-model="selectedFiles"
                />
              </td>
              <td class="py-3 px-4">
                <div class="flex items-center space-x-3">
                  <div class="file-icon">
                    <img
                      v-if="file.type.startsWith('image/')"
                      :src="file.thumbnailUrl || file.cdnUrl || file.url"
                      :alt="file.name"
                      class="w-8 h-8 object-cover rounded"
                    />
                    <component
                      v-else
                      :is="getFileIcon(file.type)"
                      class="h-6 w-6 text-gray-400"
                    />
                  </div>
                  <div>
                    <p class="font-medium">{{ file.name }}</p>
                    <p class="text-sm text-gray-500">{{ file.originalName }}</p>
                  </div>
                </div>
              </td>
              <td class="py-3 px-4">
                <span class="badge badge-outline">{{ getFileTypeText(file.type) }}</span>
              </td>
              <td class="py-3 px-4">{{ formatFileSize(file.size) }}</td>
              <td class="py-3 px-4">{{ formatDate(file.createdAt) }}</td>
              <td class="py-3 px-4">
                <div class="flex items-center space-x-2">
                  <button
                    @click="previewFile(file)"
                    class="btn btn-ghost btn-sm"
                    title="预览"
                  >
                    <EyeIcon class="h-4 w-4" />
                  </button>
                  <button
                    @click="copyFileUrl(file)"
                    class="btn btn-ghost btn-sm"
                    title="复制链接"
                  >
                    <CopyIcon class="h-4 w-4" />
                  </button>
                  <button
                    @click="downloadFile(file)"
                    class="btn btn-ghost btn-sm"
                    title="下载"
                  >
                    <DownloadIcon class="h-4 w-4" />
                  </button>
                  <button
                    @click="deleteFile(file)"
                    class="btn btn-ghost btn-sm text-red-600"
                    title="删除"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="flex items-center justify-between mt-6">
      <p class="text-sm text-gray-700">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalFiles) }} 条，
        共 {{ totalFiles }} 条记录
      </p>
      <div class="flex items-center space-x-2">
        <button
          @click="changePage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="btn btn-outline btn-sm"
        >
          上一页
        </button>
        <span class="text-sm">第 {{ currentPage }} / {{ totalPages }} 页</span>
        <button
          @click="changePage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="btn btn-outline btn-sm"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 上传模态框 -->
    <div v-if="showUploadModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">上传文件</h3>
          <button @click="closeUploadModal" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
        
        <FileUpload
          :multiple="true"
          :auto-upload="true"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        />
      </div>
    </div>

    <!-- 文件预览模态框 -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">{{ previewFile.name }}</h3>
          <button @click="showPreviewModal = false" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
        
        <div class="preview-content">
          <img
            v-if="previewFile.type.startsWith('image/')"
            :src="previewFile.cdnUrl || previewFile.url"
            :alt="previewFile.name"
            class="max-w-full max-h-96 mx-auto"
          />
          <video
            v-else-if="previewFile.type.startsWith('video/')"
            :src="previewFile.cdnUrl || previewFile.url"
            controls
            class="max-w-full max-h-96 mx-auto"
          />
          <audio
            v-else-if="previewFile.type.startsWith('audio/')"
            :src="previewFile.cdnUrl || previewFile.url"
            controls
            class="w-full"
          />
          <div v-else class="text-center py-8">
            <FileIcon class="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <p class="text-gray-600">无法预览此文件类型</p>
            <button @click="downloadFile(previewFile)" class="btn btn-primary mt-4">
              下载文件
            </button>
          </div>
        </div>
        
        <div class="file-details mt-6 p-4 bg-gray-50 rounded-lg">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium">文件名：</span>
              {{ previewFile.name }}
            </div>
            <div>
              <span class="font-medium">文件大小：</span>
              {{ formatFileSize(previewFile.size) }}
            </div>
            <div>
              <span class="font-medium">文件类型：</span>
              {{ getFileTypeText(previewFile.type) }}
            </div>
            <div>
              <span class="font-medium">上传时间：</span>
              {{ formatDate(previewFile.createdAt) }}
            </div>
            <div class="col-span-2">
              <span class="font-medium">文件链接：</span>
              <div class="flex items-center space-x-2 mt-1">
                <input
                  :value="previewFile.cdnUrl || previewFile.url"
                  readonly
                  class="input flex-1 text-xs"
                />
                <button
                  @click="copyFileUrl(previewFile)"
                  class="btn btn-outline btn-sm"
                >
                  复制
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { fileAPI } from '../services/api.js'
import FileUpload from './FileUpload.vue'
import {
  GridIcon,
  ListIcon,
  UploadIcon,
  TrashIcon,
  ArrowUpDownIcon,
  FolderIcon,
  EyeIcon,
  DownloadIcon,
  CopyIcon,
  XIcon,
  FileIcon,
  ImageIcon,
  VideoIcon,
  AudioIcon,
  FileTextIcon
} from 'lucide-vue-next'

// 响应式数据
const loading = ref(false)
const files = ref([])
const selectedFiles = ref([])
const currentView = ref('grid')
const sortBy = ref('createdAt')
const sortOrder = ref('desc')
const searchQuery = ref('')
const typeFilter = ref('')
const sizeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalFiles = ref(0)
const totalPages = ref(0)

// 模态框
const showUploadModal = ref(false)
const showPreviewModal = ref(false)
const previewFile = ref(null)

// 视图模式
const viewModes = [
  { mode: 'grid', icon: GridIcon },
  { mode: 'list', icon: ListIcon }
]

// 计算属性
const allSelected = computed(() => {
  return files.value.length > 0 && selectedFiles.value.length === files.value.length
})

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadFiles()
  }, 500)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取文件图标
const getFileIcon = (type) => {
  if (type.startsWith('image/')) return ImageIcon
  if (type.startsWith('video/')) return VideoIcon
  if (type.startsWith('audio/')) return AudioIcon
  if (type.includes('text') || type.includes('document')) return FileTextIcon
  return FileIcon
}

// 获取文件类型文本
const getFileTypeText = (type) => {
  if (type.startsWith('image/')) return '图片'
  if (type.startsWith('video/')) return '视频'
  if (type.startsWith('audio/')) return '音频'
  if (type.includes('pdf')) return 'PDF'
  if (type.includes('word') || type.includes('document')) return '文档'
  if (type.includes('text')) return '文本'
  return '文件'
}

// 加载文件列表
const loadFiles = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      search: searchQuery.value,
      type: typeFilter.value,
      size: sizeFilter.value
    }
    
    const response = await fileAPI.getFiles(params)
    
    if (response.code === 0 && response.data) {
      if (Array.isArray(response.data)) {
        files.value = response.data
        totalFiles.value = response.data.length
        totalPages.value = 1
      } else {
        files.value = response.data.content || []
        totalFiles.value = response.data.totalElements || 0
        totalPages.value = response.data.totalPages || 0
      }
    }
  } catch (err) {
    console.error('Failed to load files:', err)
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadFiles()
  }
}

// 选择文件
const selectFile = (file) => {
  const index = selectedFiles.value.indexOf(file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file.id)
  }
}

// 全选/取消全选
const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedFiles.value = []
  } else {
    selectedFiles.value = files.value.map(file => file.id)
  }
}

// 预览文件
const previewFile = (file) => {
  previewFile.value = file
  showPreviewModal.value = true
}

// 下载文件
const downloadFile = (file) => {
  const link = document.createElement('a')
  link.href = file.cdnUrl || file.url
  link.download = file.originalName || file.name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 复制文件链接
const copyFileUrl = async (file) => {
  try {
    await navigator.clipboard.writeText(file.cdnUrl || file.url)
    alert('链接已复制到剪贴板')
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = file.cdnUrl || file.url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert('链接已复制到剪贴板')
  }
}

// 删除文件
const deleteFile = async (file) => {
  if (!confirm(`确定要删除文件 "${file.name}" 吗？`)) return

  try {
    const response = await fileAPI.deleteFile(file.id)
    
    if (response.code === 0) {
      loadFiles()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (err) {
    console.error('Failed to delete file:', err)
    alert(err.message || '删除失败')
  }
}

// 批量删除文件
const deleteSelectedFiles = async () => {
  if (selectedFiles.value.length === 0) return
  
  if (!confirm(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`)) return

  try {
    const response = await fileAPI.batchDeleteFiles(selectedFiles.value)
    
    if (response.code === 0) {
      selectedFiles.value = []
      loadFiles()
    } else {
      throw new Error(response.message || '批量删除失败')
    }
  } catch (err) {
    console.error('Failed to batch delete files:', err)
    alert(err.message || '批量删除失败')
  }
}

// 处理上传成功
const handleUploadSuccess = (result) => {
  console.log('Upload success:', result)
  loadFiles()
}

// 处理上传错误
const handleUploadError = (error) => {
  console.error('Upload error:', error)
}

// 关闭上传模态框
const closeUploadModal = () => {
  showUploadModal.value = false
}

onMounted(() => {
  loadFiles()
})
</script>

<style scoped>
.file-card {
  @apply p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors relative;
}

.file-card-selected {
  @apply border-blue-500 bg-blue-50;
}

.file-actions {
  @apply absolute top-2 right-2 flex items-center space-x-1 opacity-0 transition-opacity;
}

.file-card:hover .file-actions {
  @apply opacity-100;
}

.toolbar {
  @apply border-b border-gray-200 pb-4;
}
</style>
