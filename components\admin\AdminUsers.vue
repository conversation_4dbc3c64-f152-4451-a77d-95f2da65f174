<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
        <p class="text-gray-600">管理系统中的所有用户</p>
      </div>
      <button @click="showCreateModal = true" class="btn btn-primary">
        <PlusIcon class="h-4 w-4 mr-2" />
        添加用户
      </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="card-content p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">搜索用户</label>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="用户名、邮箱..."
              class="input"
              @input="debouncedSearch"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">用户状态</label>
            <select v-model="statusFilter" @change="loadUsers" class="input">
              <option value="">全部状态</option>
              <option value="ACTIVE">正常</option>
              <option value="INACTIVE">未激活</option>
              <option value="BANNED">已封禁</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">用户角色</label>
            <select v-model="roleFilter" @change="loadUsers" class="input">
              <option value="">全部角色</option>
              <option value="USER">普通用户</option>
              <option value="ADMIN">管理员</option>
              <option value="MODERATOR">版主</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">注册时间</label>
            <select v-model="timeFilter" @change="loadUsers" class="input">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-4 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="users.length === 0" class="text-center py-12">
          <UsersIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-600">没有找到用户</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-200">
                <th class="text-left py-4 px-6 font-medium text-gray-900">用户</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">邮箱</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">角色</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">状态</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">注册时间</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">最后登录</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in users" :key="user.id" class="border-b border-gray-100 hover:bg-gray-50">
                <td class="py-4 px-6">
                  <div class="flex items-center space-x-3">
                    <div class="avatar w-10 h-10">
                      <img :src="user.avatar || '/placeholder.svg?height=40&width=40'" :alt="user.name" />
                    </div>
                    <div>
                      <p class="font-medium">{{ user.name }}</p>
                      <p class="text-sm text-gray-500">ID: {{ user.id }}</p>
                    </div>
                  </div>
                </td>
                <td class="py-4 px-6">
                  <p class="text-sm">{{ user.email }}</p>
                  <p v-if="user.phone" class="text-xs text-gray-500">{{ user.phone }}</p>
                </td>
                <td class="py-4 px-6">
                  <span :class="[
                    'badge',
                    user.role === 'ADMIN' ? 'badge-primary' : 
                    user.role === 'MODERATOR' ? 'badge-secondary' : 'badge-outline'
                  ]">
                    {{ getRoleText(user.role) }}
                  </span>
                </td>
                <td class="py-4 px-6">
                  <span :class="[
                    'badge',
                    user.status === 'ACTIVE' ? 'badge-success' : 
                    user.status === 'BANNED' ? 'badge-error' : 'badge-warning'
                  ]">
                    {{ getStatusText(user.status) }}
                  </span>
                </td>
                <td class="py-4 px-6">
                  <p class="text-sm">{{ formatDate(user.createdAt) }}</p>
                </td>
                <td class="py-4 px-6">
                  <p class="text-sm">{{ formatDate(user.lastLoginAt) || '从未登录' }}</p>
                </td>
                <td class="py-4 px-6">
                  <div class="flex items-center space-x-2">
                    <button
                      @click="editUser(user)"
                      class="btn btn-ghost btn-sm"
                      title="编辑"
                    >
                      <EditIcon class="h-4 w-4" />
                    </button>
                    <button
                      @click="toggleUserStatus(user)"
                      :class="[
                        'btn btn-sm',
                        user.status === 'BANNED' ? 'btn-success' : 'btn-warning'
                      ]"
                      :title="user.status === 'BANNED' ? '解封' : '封禁'"
                    >
                      {{ user.status === 'BANNED' ? '解封' : '封禁' }}
                    </button>
                    <button
                      @click="deleteUser(user)"
                      class="btn btn-error btn-sm"
                      title="删除"
                    >
                      <TrashIcon class="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between mt-6 px-6 pb-6">
          <p class="text-sm text-gray-700">
            显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalUsers) }} 条，
            共 {{ totalUsers }} 条记录
          </p>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="btn btn-outline btn-sm"
            >
              上一页
            </button>
            <span class="text-sm">第 {{ currentPage }} / {{ totalPages }} 页</span>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="btn btn-outline btn-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">
          {{ showCreateModal ? '添加用户' : '编辑用户' }}
        </h3>
        
        <form @submit.prevent="submitUserForm" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">用户名 *</label>
            <input
              v-model="userForm.name"
              type="text"
              class="input"
              required
              :disabled="submitting"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">邮箱 *</label>
            <input
              v-model="userForm.email"
              type="email"
              class="input"
              required
              :disabled="submitting"
            />
          </div>
          
          <div v-if="showCreateModal">
            <label class="block text-sm font-medium text-gray-700 mb-2">密码 *</label>
            <input
              v-model="userForm.password"
              type="password"
              class="input"
              required
              :disabled="submitting"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">角色</label>
            <select v-model="userForm.role" class="input" :disabled="submitting">
              <option value="USER">普通用户</option>
              <option value="MODERATOR">版主</option>
              <option value="ADMIN">管理员</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
            <select v-model="userForm.status" class="input" :disabled="submitting">
              <option value="ACTIVE">正常</option>
              <option value="INACTIVE">未激活</option>
              <option value="BANNED">已封禁</option>
            </select>
          </div>
          
          <div class="flex items-center justify-end space-x-4 pt-4">
            <button
              type="button"
              @click="closeUserModal"
              class="btn btn-outline"
              :disabled="submitting"
            >
              取消
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="submitting"
            >
              {{ submitting ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../../services/api.js'
import { 
  PlusIcon,
  UsersIcon,
  EditIcon,
  TrashIcon
} from 'lucide-vue-next'

// 响应式数据
const users = ref([])
const loading = ref(false)
const submitting = ref(false)

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const roleFilter = ref('')
const timeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)
const totalPages = ref(0)

// 模态框
const showCreateModal = ref(false)
const showEditModal = ref(false)
const userForm = ref({
  id: null,
  name: '',
  email: '',
  password: '',
  role: 'USER',
  status: 'ACTIVE'
})

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadUsers()
  }, 500)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取角色文本
const getRoleText = (role) => {
  const roleMap = {
    'USER': '普通用户',
    'MODERATOR': '版主',
    'ADMIN': '管理员'
  }
  return roleMap[role] || role
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '正常',
    'INACTIVE': '未激活',
    'BANNED': '已封禁'
  }
  return statusMap[status] || status
}

// 加载用户列表
const loadUsers = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value,
      role: roleFilter.value,
      timeRange: timeFilter.value
    }
    
    const response = await adminAPI.getUsers(params)
    
    if (response.code === 0 && response.data) {
      if (Array.isArray(response.data)) {
        users.value = response.data
        totalUsers.value = response.data.length
        totalPages.value = 1
      } else {
        users.value = response.data.content || []
        totalUsers.value = response.data.totalElements || 0
        totalPages.value = response.data.totalPages || 0
      }
    }
  } catch (err) {
    console.error('Failed to load users:', err)
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadUsers()
  }
}

// 编辑用户
const editUser = (user) => {
  userForm.value = {
    id: user.id,
    name: user.name,
    email: user.email,
    password: '',
    role: user.role,
    status: user.status
  }
  showEditModal.value = true
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  const action = user.status === 'BANNED' ? '解封' : '封禁'
  if (!confirm(`确定要${action}用户 ${user.name} 吗？`)) return

  try {
    const newStatus = user.status === 'BANNED' ? 'ACTIVE' : 'BANNED'
    const response = await adminAPI.updateUserStatus(user.id, newStatus)
    
    if (response.code === 0) {
      user.status = newStatus
    } else {
      throw new Error(response.message || `${action}失败`)
    }
  } catch (err) {
    console.error(`Failed to toggle user status:`, err)
    alert(err.message || `${action}失败`)
  }
}

// 删除用户
const deleteUser = async (user) => {
  if (!confirm(`确定要删除用户 ${user.name} 吗？此操作不可恢复！`)) return

  try {
    const response = await adminAPI.deleteUser(user.id)
    
    if (response.code === 0) {
      loadUsers()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (err) {
    console.error('Failed to delete user:', err)
    alert(err.message || '删除失败')
  }
}

// 提交用户表单
const submitUserForm = async () => {
  try {
    submitting.value = true
    
    let response
    if (showCreateModal.value) {
      response = await adminAPI.createUser(userForm.value)
    } else {
      response = await adminAPI.updateUser(userForm.value.id, userForm.value)
    }
    
    if (response.code === 0) {
      closeUserModal()
      loadUsers()
    } else {
      throw new Error(response.message || '保存失败')
    }
  } catch (err) {
    console.error('Failed to submit user form:', err)
    alert(err.message || '保存失败')
  } finally {
    submitting.value = false
  }
}

// 关闭用户模态框
const closeUserModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  userForm.value = {
    id: null,
    name: '',
    email: '',
    password: '',
    role: 'USER',
    status: 'ACTIVE'
  }
}

onMounted(() => {
  loadUsers()
})
</script>
