<template>
  <div class="space-y-8">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">CDN设置</h1>
      <p class="text-gray-600">配置内容分发网络以加速文件访问</p>
    </div>

    <!-- CDN基本配置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">基本配置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <label class="font-medium text-gray-900">启用CDN</label>
            <p class="text-sm text-gray-500">使用CDN加速文件访问</p>
          </div>
          <input
            v-model="settings.enabled"
            type="checkbox"
            class="toggle"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">CDN域名</label>
            <input
              v-model="settings.domain"
              type="text"
              class="input"
              placeholder="https://cdn.example.com"
              :disabled="!settings.enabled"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">源站域名</label>
            <input
              v-model="settings.origin"
              type="text"
              class="input"
              placeholder="https://api.example.com"
              :disabled="!settings.enabled"
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">CDN提供商</label>
          <select v-model="settings.provider" class="input" :disabled="!settings.enabled">
            <option value="aliyun">阿里云CDN</option>
            <option value="tencent">腾讯云CDN</option>
            <option value="qiniu">七牛云CDN</option>
            <option value="upyun">又拍云CDN</option>
            <option value="aws">AWS CloudFront</option>
            <option value="cloudflare">Cloudflare</option>
            <option value="custom">自定义</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 缓存配置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">缓存配置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">图片缓存时间 (秒)</label>
            <input
              v-model.number="settings.cache.image"
              type="number"
              class="input"
              placeholder="86400"
              :disabled="!settings.enabled"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">视频缓存时间 (秒)</label>
            <input
              v-model.number="settings.cache.video"
              type="number"
              class="input"
              placeholder="604800"
              :disabled="!settings.enabled"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">文档缓存时间 (秒)</label>
            <input
              v-model.number="settings.cache.document"
              type="number"
              class="input"
              placeholder="3600"
              :disabled="!settings.enabled"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">其他文件缓存时间 (秒)</label>
            <input
              v-model.number="settings.cache.other"
              type="number"
              class="input"
              placeholder="1800"
              :disabled="!settings.enabled"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <label class="font-medium text-gray-900">启用浏览器缓存</label>
            <p class="text-sm text-gray-500">设置浏览器缓存头</p>
          </div>
          <input
            v-model="settings.browserCache"
            type="checkbox"
            class="toggle"
            :disabled="!settings.enabled"
          />
        </div>
      </div>
    </div>

    <!-- 图片处理 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">图片处理</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <label class="font-medium text-gray-900">启用图片处理</label>
            <p class="text-sm text-gray-500">自动生成不同尺寸的图片</p>
          </div>
          <input
            v-model="settings.imageProcessing.enabled"
            type="checkbox"
            class="toggle"
            :disabled="!settings.enabled"
          />
        </div>

        <div v-if="settings.imageProcessing.enabled" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">图片质量 (1-100)</label>
            <input
              v-model.number="settings.imageProcessing.quality"
              type="number"
              min="1"
              max="100"
              class="input"
              placeholder="80"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">预设尺寸</label>
            <div class="space-y-2">
              <div v-for="(size, index) in settings.imageProcessing.sizes" :key="index" class="flex items-center space-x-2">
                <input
                  v-model="size.name"
                  type="text"
                  placeholder="尺寸名称"
                  class="input flex-1"
                />
                <input
                  v-model.number="size.width"
                  type="number"
                  placeholder="宽度"
                  class="input w-24"
                />
                <span class="text-gray-500">×</span>
                <input
                  v-model.number="size.height"
                  type="number"
                  placeholder="高度"
                  class="input w-24"
                />
                <button
                  @click="removeImageSize(index)"
                  class="btn btn-ghost btn-sm text-red-600"
                >
                  <XIcon class="h-4 w-4" />
                </button>
              </div>
              <button
                @click="addImageSize"
                class="btn btn-outline btn-sm"
              >
                <PlusIcon class="h-4 w-4 mr-2" />
                添加尺寸
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <label class="font-medium text-gray-900">WebP格式转换</label>
              <p class="text-sm text-gray-500">自动转换为WebP格式以减小文件大小</p>
            </div>
            <input
              v-model="settings.imageProcessing.webp"
              type="checkbox"
              class="toggle"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 安全配置 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">安全配置</h3>
      </div>
      <div class="card-content p-6 space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <label class="font-medium text-gray-900">防盗链保护</label>
            <p class="text-sm text-gray-500">防止其他网站直接引用文件</p>
          </div>
          <input
            v-model="settings.security.hotlinkProtection"
            type="checkbox"
            class="toggle"
            :disabled="!settings.enabled"
          />
        </div>

        <div v-if="settings.security.hotlinkProtection">
          <label class="block text-sm font-medium text-gray-700 mb-2">允许的域名</label>
          <textarea
            v-model="settings.security.allowedDomains"
            class="textarea"
            rows="3"
            placeholder="每行一个域名，如：example.com"
          ></textarea>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <label class="font-medium text-gray-900">访问签名</label>
            <p class="text-sm text-gray-500">为文件访问生成时效性签名</p>
          </div>
          <input
            v-model="settings.security.signedUrls"
            type="checkbox"
            class="toggle"
            :disabled="!settings.enabled"
          />
        </div>

        <div v-if="settings.security.signedUrls" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">签名密钥</label>
            <input
              v-model="settings.security.signKey"
              type="password"
              class="input"
              placeholder="输入签名密钥"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">签名有效期 (秒)</label>
            <input
              v-model.number="settings.security.signExpiry"
              type="number"
              class="input"
              placeholder="3600"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-semibold">CDN统计</h3>
      </div>
      <div class="card-content p-6">
        <div v-if="loadingStats" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <p class="mt-2 text-gray-600">加载统计数据...</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ stats.totalFiles || 0 }}</div>
            <div class="text-sm text-gray-600">总文件数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ formatFileSize(stats.totalSize || 0) }}</div>
            <div class="text-sm text-gray-600">总存储大小</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ stats.todayRequests || 0 }}</div>
            <div class="text-sm text-gray-600">今日请求数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">{{ formatFileSize(stats.todayTraffic || 0) }}</div>
            <div class="text-sm text-gray-600">今日流量</div>
          </div>
        </div>

        <div class="mt-6 flex items-center space-x-4">
          <button @click="refreshStats" :disabled="loadingStats" class="btn btn-outline">
            <RefreshCwIcon :class="['h-4 w-4 mr-2', loadingStats ? 'animate-spin' : '']" />
            刷新统计
          </button>
          <button @click="testCDN" :disabled="testingCDN" class="btn btn-outline">
            {{ testingCDN ? '测试中...' : '测试CDN连接' }}
          </button>
          <button @click="purgeCache" :disabled="purgingCache" class="btn btn-outline">
            {{ purgingCache ? '清理中...' : '清理缓存' }}
          </button>
        </div>

        <div v-if="testResult" class="mt-4 p-3 rounded-lg" :class="testResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'">
          {{ testResult.message }}
        </div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="flex items-center justify-end space-x-4">
      <button @click="resetSettings" class="btn btn-outline" :disabled="saving">
        重置
      </button>
      <button @click="saveSettings" class="btn btn-primary" :disabled="saving">
        {{ saving ? '保存中...' : '保存设置' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI, fileAPI } from '../../services/api.js'
import {
  XIcon,
  PlusIcon,
  RefreshCwIcon
} from 'lucide-vue-next'

// 响应式数据
const saving = ref(false)
const loadingStats = ref(false)
const testingCDN = ref(false)
const purgingCache = ref(false)
const stats = ref({})
const testResult = ref(null)

// CDN设置
const settings = ref({
  enabled: false,
  domain: '',
  origin: '',
  provider: 'aliyun',
  cache: {
    image: 86400,
    video: 604800,
    document: 3600,
    other: 1800
  },
  browserCache: true,
  imageProcessing: {
    enabled: false,
    quality: 80,
    webp: true,
    sizes: [
      { name: 'thumbnail', width: 150, height: 150 },
      { name: 'small', width: 300, height: 300 },
      { name: 'medium', width: 600, height: 600 },
      { name: 'large', width: 1200, height: 1200 }
    ]
  },
  security: {
    hotlinkProtection: false,
    allowedDomains: '',
    signedUrls: false,
    signKey: '',
    signExpiry: 3600
  }
})

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 添加图片尺寸
const addImageSize = () => {
  settings.value.imageProcessing.sizes.push({
    name: '',
    width: 0,
    height: 0
  })
}

// 移除图片尺寸
const removeImageSize = (index) => {
  settings.value.imageProcessing.sizes.splice(index, 1)
}

// 加载设置
const loadSettings = async () => {
  try {
    const response = await adminAPI.getCDNSettings()
    
    if (response.code === 0 && response.data) {
      Object.assign(settings.value, response.data)
    }
  } catch (err) {
    console.error('Failed to load CDN settings:', err)
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    saving.value = true
    
    const response = await adminAPI.updateCDNSettings(settings.value)
    
    if (response.code === 0) {
      alert('CDN设置保存成功')
    } else {
      throw new Error(response.message || '保存失败')
    }
  } catch (err) {
    console.error('Failed to save CDN settings:', err)
    alert(err.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  if (confirm('确定要重置CDN设置吗？')) {
    loadSettings()
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    loadingStats.value = true
    
    const response = await fileAPI.getFileStats()
    
    if (response.code === 0 && response.data) {
      stats.value = response.data
    }
  } catch (err) {
    console.error('Failed to load stats:', err)
  } finally {
    loadingStats.value = false
  }
}

// 刷新统计
const refreshStats = () => {
  loadStats()
}

// 测试CDN连接
const testCDN = async () => {
  try {
    testingCDN.value = true
    testResult.value = null
    
    // 这里需要一个测试CDN的API
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    testResult.value = {
      success: true,
      message: 'CDN连接测试成功'
    }
  } catch (err) {
    testResult.value = {
      success: false,
      message: 'CDN连接测试失败: ' + err.message
    }
  } finally {
    testingCDN.value = false
  }
}

// 清理缓存
const purgeCache = async () => {
  if (!confirm('确定要清理CDN缓存吗？')) return

  try {
    purgingCache.value = true
    
    // 这里需要一个清理缓存的API
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    alert('CDN缓存清理成功')
  } catch (err) {
    alert('CDN缓存清理失败: ' + err.message)
  } finally {
    purgingCache.value = false
  }
}

onMounted(() => {
  loadSettings()
  loadStats()
})
</script>

<style scoped>
.toggle {
  @apply relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.toggle:checked {
  @apply bg-blue-600;
}

.toggle::before {
  @apply absolute left-1 top-1 h-4 w-4 rounded-full bg-white transition-transform;
  content: '';
}

.toggle:checked::before {
  @apply translate-x-5;
}
</style>
