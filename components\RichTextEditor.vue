<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <div class="toolbar-group">
        <button
          v-for="tool in formatTools"
          :key="tool.command"
          @click="execCommand(tool.command, tool.value)"
          :class="[
            'toolbar-btn',
            isActive(tool.command) ? 'toolbar-btn-active' : ''
          ]"
          :title="tool.title"
        >
          <component :is="tool.icon" class="h-4 w-4" />
        </button>
      </div>
      
      <div class="toolbar-divider"></div>
      
      <div class="toolbar-group">
        <button
          @click="showImageUpload = true"
          class="toolbar-btn"
          title="插入图片"
        >
          <ImageIcon class="h-4 w-4" />
        </button>
        <button
          @click="showFileUpload = true"
          class="toolbar-btn"
          title="插入文件"
        >
          <PaperclipIcon class="h-4 w-4" />
        </button>
        <button
          @click="insertLink"
          class="toolbar-btn"
          title="插入链接"
        >
          <LinkIcon class="h-4 w-4" />
        </button>
      </div>
      
      <div class="toolbar-divider"></div>
      
      <div class="toolbar-group">
        <button
          @click="toggleFullscreen"
          class="toolbar-btn"
          :title="isFullscreen ? '退出全屏' : '全屏编辑'"
        >
          <component :is="isFullscreen ? MinimizeIcon : MaximizeIcon" class="h-4 w-4" />
        </button>
      </div>
    </div>

    <div
      ref="editor"
      :class="[
        'editor-content',
        isFullscreen ? 'editor-fullscreen' : ''
      ]"
      contenteditable="true"
      @input="handleInput"
      @paste="handlePaste"
      @keydown="handleKeydown"
      :placeholder="placeholder"
    ></div>

    <div v-if="showWordCount" class="editor-footer">
      <span class="text-sm text-gray-500">
        字数: {{ wordCount }} / {{ maxLength || '∞' }}
      </span>
    </div>

    <!-- 图片上传模态框 -->
    <div v-if="showImageUpload" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">插入图片</h3>
          <button @click="showImageUpload = false" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">图片链接</label>
            <input
              v-model="imageUrl"
              type="url"
              placeholder="https://example.com/image.jpg"
              class="input"
            />
          </div>
          
          <div class="text-center text-gray-500">或</div>
          
          <FileUpload
            :multiple="false"
            accept="image/*"
            :auto-upload="true"
            @upload-success="handleImageUpload"
          />
          
          <div class="flex items-center justify-end space-x-4">
            <button @click="showImageUpload = false" class="btn btn-outline">
              取消
            </button>
            <button
              @click="insertImage"
              :disabled="!imageUrl"
              class="btn btn-primary"
            >
              插入图片
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件上传模态框 -->
    <div v-if="showFileUpload" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">插入文件</h3>
          <button @click="showFileUpload = false" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
        
        <FileUpload
          :multiple="true"
          :auto-upload="true"
          @upload-success="handleFileUpload"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import FileUpload from './FileUpload.vue'
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  AlignLeftIcon,
  AlignCenterIcon,
  AlignRightIcon,
  ListIcon,
  ListOrderedIcon,
  QuoteIcon,
  CodeIcon,
  ImageIcon,
  PaperclipIcon,
  LinkIcon,
  MaximizeIcon,
  MinimizeIcon,
  XIcon
} from 'lucide-vue-next'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  maxLength: {
    type: Number,
    default: null
  },
  showWordCount: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const editor = ref(null)
const isFullscreen = ref(false)
const showImageUpload = ref(false)
const showFileUpload = ref(false)
const imageUrl = ref('')

// 工具栏配置
const formatTools = [
  { command: 'bold', icon: BoldIcon, title: '粗体' },
  { command: 'italic', icon: ItalicIcon, title: '斜体' },
  { command: 'underline', icon: UnderlineIcon, title: '下划线' },
  { command: 'strikeThrough', icon: StrikethroughIcon, title: '删除线' },
  { command: 'justifyLeft', icon: AlignLeftIcon, title: '左对齐' },
  { command: 'justifyCenter', icon: AlignCenterIcon, title: '居中对齐' },
  { command: 'justifyRight', icon: AlignRightIcon, title: '右对齐' },
  { command: 'insertUnorderedList', icon: ListIcon, title: '无序列表' },
  { command: 'insertOrderedList', icon: ListOrderedIcon, title: '有序列表' },
  { command: 'formatBlock', value: 'blockquote', icon: QuoteIcon, title: '引用' },
  { command: 'formatBlock', value: 'pre', icon: CodeIcon, title: '代码块' }
]

// 计算属性
const wordCount = computed(() => {
  if (!editor.value) return 0
  const text = editor.value.textContent || ''
  return text.length
})

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.innerHTML !== newValue) {
    editor.value.innerHTML = newValue
  }
}, { immediate: true })

// 执行编辑命令
const execCommand = (command, value = null) => {
  document.execCommand(command, false, value)
  editor.value.focus()
}

// 检查命令是否激活
const isActive = (command) => {
  return document.queryCommandState(command)
}

// 处理输入
const handleInput = () => {
  const content = editor.value.innerHTML
  
  // 检查字数限制
  if (props.maxLength && wordCount.value > props.maxLength) {
    return
  }
  
  emit('update:modelValue', content)
  emit('change', content)
}

// 处理粘贴
const handlePaste = (event) => {
  event.preventDefault()
  
  const clipboardData = event.clipboardData || window.clipboardData
  const pastedData = clipboardData.getData('text/plain')
  
  // 清理粘贴的内容
  const cleanedData = pastedData.replace(/<[^>]*>/g, '')
  
  document.execCommand('insertText', false, cleanedData)
}

// 处理键盘事件
const handleKeydown = (event) => {
  // Ctrl+B 粗体
  if (event.ctrlKey && event.key === 'b') {
    event.preventDefault()
    execCommand('bold')
  }
  // Ctrl+I 斜体
  else if (event.ctrlKey && event.key === 'i') {
    event.preventDefault()
    execCommand('italic')
  }
  // Ctrl+U 下划线
  else if (event.ctrlKey && event.key === 'u') {
    event.preventDefault()
    execCommand('underline')
  }
  // Enter 键处理
  else if (event.key === 'Enter') {
    // 在代码块中按Enter时插入换行而不是新段落
    if (document.queryCommandValue('formatBlock') === 'pre') {
      event.preventDefault()
      document.execCommand('insertHTML', false, '\n')
    }
  }
}

// 插入图片
const insertImage = () => {
  if (imageUrl.value) {
    const img = `<img src="${imageUrl.value}" alt="插入的图片" style="max-width: 100%; height: auto;" />`
    document.execCommand('insertHTML', false, img)
    imageUrl.value = ''
    showImageUpload.value = false
    editor.value.focus()
  }
}

// 处理图片上传成功
const handleImageUpload = (result) => {
  const img = `<img src="${result.cdnUrl || result.url}" alt="${result.file.name}" style="max-width: 100%; height: auto;" />`
  document.execCommand('insertHTML', false, img)
  showImageUpload.value = false
  editor.value.focus()
}

// 处理文件上传成功
const handleFileUpload = (result) => {
  const link = `<a href="${result.cdnUrl || result.url}" target="_blank">${result.file.name}</a>`
  document.execCommand('insertHTML', false, link)
  showFileUpload.value = false
  editor.value.focus()
}

// 插入链接
const insertLink = () => {
  const url = prompt('请输入链接地址:')
  if (url) {
    const selection = window.getSelection()
    const text = selection.toString() || url
    const link = `<a href="${url}" target="_blank">${text}</a>`
    document.execCommand('insertHTML', false, link)
    editor.value.focus()
  }
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// 键盘快捷键监听
const handleGlobalKeydown = (event) => {
  // ESC 退出全屏
  if (event.key === 'Escape' && isFullscreen.value) {
    toggleFullscreen()
  }
}

onMounted(() => {
  if (editor.value) {
    editor.value.innerHTML = props.modelValue
  }
  
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
  document.body.style.overflow = ''
})

// 暴露方法给父组件
defineExpose({
  focus: () => editor.value?.focus(),
  blur: () => editor.value?.blur(),
  getContent: () => editor.value?.innerHTML || '',
  setContent: (content) => {
    if (editor.value) {
      editor.value.innerHTML = content
    }
  },
  insertText: (text) => {
    document.execCommand('insertText', false, text)
  },
  insertHTML: (html) => {
    document.execCommand('insertHTML', false, html)
  }
})
</script>

<style scoped>
.rich-text-editor {
  @apply border border-gray-300 rounded-lg overflow-hidden;
}

.editor-toolbar {
  @apply flex items-center space-x-1 p-2 bg-gray-50 border-b border-gray-200;
}

.toolbar-group {
  @apply flex items-center space-x-1;
}

.toolbar-divider {
  @apply w-px h-6 bg-gray-300 mx-2;
}

.toolbar-btn {
  @apply p-2 rounded hover:bg-gray-200 transition-colors;
}

.toolbar-btn-active {
  @apply bg-blue-100 text-blue-600;
}

.editor-content {
  @apply p-4 min-h-[200px] max-h-[400px] overflow-y-auto focus:outline-none;
}

.editor-content:empty::before {
  content: attr(placeholder);
  @apply text-gray-400;
}

.editor-fullscreen {
  @apply fixed inset-0 z-40 bg-white max-h-none;
  padding: 2rem;
}

.editor-footer {
  @apply px-4 py-2 bg-gray-50 border-t border-gray-200 text-right;
}

/* 编辑器内容样式 */
.editor-content :global(h1) {
  @apply text-2xl font-bold mb-4;
}

.editor-content :global(h2) {
  @apply text-xl font-bold mb-3;
}

.editor-content :global(h3) {
  @apply text-lg font-bold mb-2;
}

.editor-content :global(p) {
  @apply mb-2;
}

.editor-content :global(blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
}

.editor-content :global(pre) {
  @apply bg-gray-100 p-4 rounded font-mono text-sm overflow-x-auto my-4;
}

.editor-content :global(ul) {
  @apply list-disc list-inside mb-4;
}

.editor-content :global(ol) {
  @apply list-decimal list-inside mb-4;
}

.editor-content :global(a) {
  @apply text-blue-600 hover:underline;
}

.editor-content :global(img) {
  @apply max-w-full h-auto rounded my-2;
}
</style>
