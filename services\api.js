import request from "some-request-library" // Assuming a request library is used

// 管理员API
export const adminAPI = {
  // 获取系统统计数据
  getSystemStats: () => request("/admin/stats"),

  // 获取最新用户
  getRecentUsers: (limit = 10) => request(`/admin/users/recent?limit=${limit}`),

  // 获取最新内容
  getRecentContent: (limit = 10) => request(`/admin/content/recent?limit=${limit}`),

  // 用户管理
  getUsers: (params) => request("/admin/users", { params }),
  createUser: (userData) =>
    request("/admin/users", {
      method: "POST",
      data: userData,
    }),
  updateUser: (userId, userData) =>
    request(`/admin/users/${userId}`, {
      method: "PUT",
      data: userData,
    }),
  updateUserStatus: (userId, status) =>
    request(`/admin/users/${userId}/status`, {
      method: "PUT",
      data: { status },
    }),
  deleteUser: (userId) =>
    request(`/admin/users/${userId}`, {
      method: "DELETE",
    }),

  // 内容管理
  getContent: (params) => request("/admin/content", { params }),
  approveContent: (contentId) =>
    request(`/admin/content/${contentId}/approve`, {
      method: "PUT",
    }),
  rejectContent: (contentId, reason) =>
    request(`/admin/content/${contentId}/reject`, {
      method: "PUT",
      data: { reason },
    }),
  deleteContent: (contentId) =>
    request(`/admin/content/${contentId}`, {
      method: "DELETE",
    }),
  batchApproveContent: (contentIds) =>
    request("/admin/content/batch/approve", {
      method: "PUT",
      data: { ids: contentIds },
    }),
  batchRejectContent: (contentIds, reason) =>
    request("/admin/content/batch/reject", {
      method: "PUT",
      data: { ids: contentIds, reason },
    }),
  batchDeleteContent: (contentIds) =>
    request("/admin/content/batch/delete", {
      method: "DELETE",
      data: { ids: contentIds },
    }),

  // 评论管理
  getComments: (params) => request("/admin/comments", { params }),
  deleteComment: (commentId) =>
    request(`/admin/comments/${commentId}`, {
      method: "DELETE",
    }),
  batchDeleteComments: (commentIds) =>
    request("/admin/comments/batch/delete", {
      method: "DELETE",
      data: { ids: commentIds },
    }),

  // 举报管理
  getReports: (params) => request("/admin/reports", { params }),
  handleReport: (reportId, action, reason) =>
    request(`/admin/reports/${reportId}/handle`, {
      method: "PUT",
      data: { action, reason },
    }),

  // 分类管理
  getCategories: () => request("/admin/categories"),
  createCategory: (categoryData) =>
    request("/admin/categories", {
      method: "POST",
      data: categoryData,
    }),
  updateCategory: (categoryId, categoryData) =>
    request(`/admin/categories/${categoryId}`, {
      method: "PUT",
      data: categoryData,
    }),
  deleteCategory: (categoryId) =>
    request(`/admin/categories/${categoryId}`, {
      method: "DELETE",
    }),

  // 系统设置
  getSystemSettings: () => request("/admin/settings"),
  updateSystemSettings: (settings) =>
    request("/admin/settings", {
      method: "PUT",
      data: settings,
    }),

  // 操作日志
  getLogs: (params) => request("/admin/logs", { params }),

  // 数据导出
  exportUsers: (params) =>
    request("/admin/export/users", {
      params,
      responseType: "blob",
    }),
  exportContent: (params) =>
    request("/admin/export/content", {
      params,
      responseType: "blob",
    }),

  // CDN设置
  getCDNSettings: () => request("/admin/cdn/settings"),
  updateCDNSettings: (settings) =>
    request("/admin/cdn/settings", {
      method: "PUT",
      data: settings,
    }),

  // CDN缓存管理
  purgeCDNCache: (urls) =>
    request("/admin/cdn/purge", {
      method: "POST",
      data: { urls },
    }),

  // CDN统计
  getCDNStats: () => request("/admin/cdn/stats"),

  // 测试CDN连接
  testCDNConnection: () => request("/admin/cdn/test"),
}

// 文件管理API
export const fileAPI = {
  // 上传文件
  uploadFile: (formData, config = {}) =>
    request("/files/upload", {
      method: "POST",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      ...config,
    }),

  // 获取文件列表
  getFiles: (params) => request("/files", { params }),

  // 获取文件详情
  getFileById: (fileId) => request(`/files/${fileId}`),

  // 删除文件
  deleteFile: (fileId) =>
    request(`/files/${fileId}`, {
      method: "DELETE",
    }),

  // 批量删除文件
  batchDeleteFiles: (fileIds) =>
    request("/files/batch/delete", {
      method: "DELETE",
      data: { ids: fileIds },
    }),

  // 生成上传签名（用于直传CDN）
  getUploadSignature: (fileName, fileType) =>
    request("/files/upload/signature", {
      method: "POST",
      data: { fileName, fileType },
    }),

  // 确认上传完成
  confirmUpload: (uploadData) =>
    request("/files/upload/confirm", {
      method: "POST",
      data: uploadData,
    }),

  // 获取文件统计
  getFileStats: () => request("/files/stats"),

  // 清理无用文件
  cleanupFiles: () =>
    request("/files/cleanup", {
      method: "POST",
    }),
}

// 图片编辑API
export const imageAPI = {
  // 图片裁剪
  cropImage: (imageData) =>
    request("/images/crop", {
      method: "POST",
      data: imageData,
    }),

  // 图片调整（亮度、对比度等）
  adjustImage: (imageData) =>
    request("/images/adjust", {
      method: "POST",
      data: imageData,
    }),

  // 应用滤镜
  applyFilter: (imageData) =>
    request("/images/filter", {
      method: "POST",
      data: imageData,
    }),

  // 图片变换（旋转、翻转、缩放）
  transformImage: (imageData) =>
    request("/images/transform", {
      method: "POST",
      data: imageData,
    }),

  // 添加文字水印
  addTextWatermark: (imageData) =>
    request("/images/watermark/text", {
      method: "POST",
      data: imageData,
    }),

  // 添加图片水印
  addImageWatermark: (imageData) =>
    request("/images/watermark/image", {
      method: "POST",
      data: imageData,
    }),

  // 批量处理图片
  batchProcess: (images, operations) =>
    request("/images/batch", {
      method: "POST",
      data: { images, operations },
    }),

  // 获取图片编辑历史
  getEditHistory: (imageId) => request(`/images/${imageId}/history`),

  // 恢复到历史版本
  restoreVersion: (imageId, versionId) =>
    request(`/images/${imageId}/restore/${versionId}`, {
      method: "POST",
    }),

  // 生成缩略图
  generateThumbnail: (imageUrl, options) =>
    request("/images/thumbnail", {
      method: "POST",
      data: { imageUrl, ...options },
    }),

  // 图片格式转换
  convertFormat: (imageData, targetFormat) =>
    request("/images/convert", {
      method: "POST",
      data: { ...imageData, format: targetFormat },
    }),

  // 图片压缩
  compressImage: (imageData, quality) =>
    request("/images/compress", {
      method: "POST",
      data: { ...imageData, quality },
    }),

  // 获取图片元数据
  getImageMetadata: (imageUrl) =>
    request("/images/metadata", {
      method: "POST",
      data: { imageUrl },
    }),

  // 移除图片背景
  removeBackground: (imageData) =>
    request("/images/remove-background", {
      method: "POST",
      data: imageData,
    }),

  // AI图片增强
  enhanceImage: (imageData, options) =>
    request("/images/enhance", {
      method: "POST",
      data: { ...imageData, ...options },
    }),
}
