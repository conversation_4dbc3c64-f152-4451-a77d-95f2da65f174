<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-full h-full max-w-7xl max-h-[95vh] flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold">编辑图片</h2>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">{{ originalFileName }}</span>
          <button @click="closeEditor" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
      </div>

      <!-- 编辑器 -->
      <div class="flex-1 overflow-hidden">
        <ImageEditor
          v-if="imageUrl"
          :image-url="imageUrl"
          @save="handleSave"
          @cancel="closeEditor"
        />
      </div>

      <!-- 底部操作栏 -->
      <div class="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-4">
          <button @click="resetToOriginal" class="btn btn-outline">
            <RotateCcwIcon class="h-4 w-4 mr-2" />
            恢复原图
          </button>
          <button @click="showPresets = !showPresets" class="btn btn-outline">
            <SettingsIcon class="h-4 w-4 mr-2" />
            快速预设
          </button>
        </div>
        
        <div class="flex items-center space-x-4">
          <button @click="closeEditor" class="btn btn-outline">
            取消
          </button>
          <button @click="saveAndClose" class="btn btn-primary">
            <SaveIcon class="h-4 w-4 mr-2" />
            保存并关闭
          </button>
        </div>
      </div>

      <!-- 快速预设面板 -->
      <div v-if="showPresets" class="absolute bottom-20 left-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
        <h4 class="font-semibold mb-3">快速预设</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            v-for="preset in quickPresets"
            :key="preset.name"
            @click="applyPreset(preset)"
            class="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <component :is="preset.icon" class="h-6 w-6 mb-2 text-gray-600" />
            <span class="text-sm">{{ preset.label }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import ImageEditor from './ImageEditor.vue'
import {
  XIcon,
  SaveIcon,
  RotateCcwIcon,
  SettingsIcon,
  CropIcon,
  SunIcon,
  ContrastIcon,
  PaletteIcon,
  FilterIcon,
  RotateCwIcon,
  ImageIcon,
  ZapIcon
} from 'lucide-vue-next'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    required: true
  },
  fileName: {
    type: String,
    default: 'image'
  }
})

const emit = defineEmits(['close', 'save'])

// 响应式数据
const showPresets = ref(false)
const editedImageData = ref(null)

// 计算属性
const originalFileName = computed(() => {
  return props.fileName || 'untitled'
})

// 快速预设配置
const quickPresets = [
  {
    name: 'crop-square',
    label: '方形裁剪',
    icon: CropIcon,
    actions: [
      { tool: 'crop', aspectRatio: 1 }
    ]
  },
  {
    name: 'brighten',
    label: '提亮',
    icon: SunIcon,
    actions: [
      { tool: 'adjust', brightness: 20, contrast: 10 }
    ]
  },
  {
    name: 'enhance',
    label: '增强',
    icon: ContrastIcon,
    actions: [
      { tool: 'adjust', contrast: 15, saturation: 10, sharpen: 2 }
    ]
  },
  {
    name: 'vintage',
    label: '复古',
    icon: PaletteIcon,
    actions: [
      { tool: 'filter', filter: 'vintage' },
      { tool: 'adjust', brightness: 5, contrast: -5 }
    ]
  },
  {
    name: 'bw',
    label: '黑白',
    icon: FilterIcon,
    actions: [
      { tool: 'filter', filter: 'grayscale' },
      { tool: 'adjust', contrast: 10 }
    ]
  },
  {
    name: 'rotate',
    label: '旋转',
    icon: RotateCwIcon,
    actions: [
      { tool: 'transform', rotation: 90 }
    ]
  },
  {
    name: 'social',
    label: '社交媒体',
    icon: ImageIcon,
    actions: [
      { tool: 'crop', aspectRatio: 1 },
      { tool: 'adjust', brightness: 10, saturation: 15 }
    ]
  },
  {
    name: 'auto-enhance',
    label: '自动增强',
    icon: ZapIcon,
    actions: [
      { tool: 'adjust', brightness: 8, contrast: 12, saturation: 8, sharpen: 1.5 }
    ]
  }
]

// 监听显示状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    showPresets.value = false
    editedImageData.value = null
  }
})

// 应用快速预设
const applyPreset = (preset) => {
  // 这里需要与ImageEditor组件通信来应用预设
  // 可以通过ref或者事件来实现
  console.log('Applying preset:', preset)
  showPresets.value = false
  
  // 触发预设应用事件
  emit('apply-preset', preset)
}

// 恢复原图
const resetToOriginal = () => {
  // 重置编辑器到原始状态
  emit('reset-to-original')
}

// 处理保存
const handleSave = (imageData) => {
  editedImageData.value = imageData
}

// 保存并关闭
const saveAndClose = () => {
  if (editedImageData.value) {
    emit('save', editedImageData.value)
  }
  closeEditor()
}

// 关闭编辑器
const closeEditor = () => {
  showPresets.value = false
  emit('close')
}
</script>

<style scoped>
/* 样式已在模板中使用Tailwind类定义 */
</style>
