<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">操作日志</h1>
        <p class="text-gray-600">查看系统操作记录</p>
      </div>
      <div class="flex items-center space-x-4">
        <button @click="exportLogs" class="btn btn-outline">
          <DownloadIcon class="h-4 w-4 mr-2" />
          导出日志
        </button>
        <button @click="clearLogs" class="btn btn-error">
          <TrashIcon class="h-4 w-4 mr-2" />
          清空日志
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="card">
      <div class="card-content p-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">操作类型</label>
            <select v-model="actionFilter" @change="loadLogs" class="input">
              <option value="">全部操作</option>
              <option value="LOGIN">登录</option>
              <option value="LOGOUT">登出</option>
              <option value="CREATE">创建</option>
              <option value="UPDATE">更新</option>
              <option value="DELETE">删除</option>
              <option value="APPROVE">审核</option>
              <option value="BAN">封禁</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">操作对象</label>
            <select v-model="targetFilter" @change="loadLogs" class="input">
              <option value="">全部对象</option>
              <option value="USER">用户</option>
              <option value="ARTICLE">文章</option>
              <option value="COMMENT">评论</option>
              <option value="SYSTEM">系统</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">操作人</label>
            <input
              v-model="operatorFilter"
              type="text"
              placeholder="用户名..."
              class="input"
              @input="debouncedSearch"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">IP地址</label>
            <input
              v-model="ipFilter"
              type="text"
              placeholder="IP地址..."
              class="input"
              @input="debouncedSearch"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
            <select v-model="timeFilter" @change="loadLogs" class="input">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-4 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="logs.length === 0" class="text-center py-12">
          <FileIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-600">没有找到日志记录</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-200">
                <th class="text-left py-4 px-6 font-medium text-gray-900">时间</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">操作人</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">操作</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">对象</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">IP地址</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">结果</th>
                <th class="text-left py-4 px-6 font-medium text-gray-900">详情</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="log in logs" :key="log.id" class="border-b border-gray-100 hover:bg-gray-50">
                <td class="py-4 px-6">
                  <p class="text-sm">{{ formatDate(log.createdAt) }}</p>
                </td>
                <td class="py-4 px-6">
                  <div class="flex items-center space-x-2">
                    <div class="avatar w-8 h-8">
                      <img :src="log.operator?.avatar || '/placeholder.svg?height=32&width=32'" :alt="log.operator?.name" />
                    </div>
                    <div>
                      <p class="font-medium text-sm">{{ log.operator?.name || '系统' }}</p>
                      <p class="text-xs text-gray-500">{{ log.operator?.role || 'SYSTEM' }}</p>
                    </div>
                  </div>
                </td>
                <td class="py-4 px-6">
                  <span :class="[
                    'badge text-xs',
                    getActionColor(log.action)
                  ]">
                    {{ getActionText(log.action) }}
                  </span>
                </td>
                <td class="py-4 px-6">
                  <div>
                    <p class="text-sm font-medium">{{ getTargetText(log.targetType) }}</p>
                    <p class="text-xs text-gray-500">ID: {{ log.targetId || 'N/A' }}</p>
                  </div>
                </td>
                <td class="py-4 px-6">
                  <p class="text-sm font-mono">{{ log.ipAddress || 'N/A' }}</p>
                  <p class="text-xs text-gray-500">{{ log.userAgent ? log.userAgent.substring(0, 20) + '...' : '' }}</p>
                </td>
                <td class="py-4 px-6">
                  <span :class="[
                    'badge text-xs',
                    log.success ? 'badge-success' : 'badge-error'
                  ]">
                    {{ log.success ? '成功' : '失败' }}
                  </span>
                </td>
                <td class="py-4 px-6">
                  <button
                    @click="viewLogDetail(log)"
                    class="btn btn-ghost btn-sm"
                    title="查看详情"
                  >
                    <EyeIcon class="h-4 w-4" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between mt-6 px-6 pb-6">
          <p class="text-sm text-gray-700">
            显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalLogs) }} 条，
            共 {{ totalLogs }} 条记录
          </p>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="btn btn-outline btn-sm"
            >
              上一页
            </button>
            <span class="text-sm">第 {{ currentPage }} / {{ totalPages }} 页</span>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="btn btn-outline btn-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志详情模态框 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">日志详情</h3>
          <button @click="showDetailModal = false" class="btn btn-ghost btn-sm">
            <XIcon class="h-4 w-4" />
          </button>
        </div>
        
        <div v-if="selectedLog" class="space-y-4">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium">操作时间：</span>
              {{ formatDate(selectedLog.createdAt) }}
            </div>
            <div>
              <span class="font-medium">操作人：</span>
              {{ selectedLog.operator?.name || '系统' }}
            </div>
            <div>
              <span class="font-medium">操作类型：</span>
              {{ getActionText(selectedLog.action) }}
            </div>
            <div>
              <span class="font-medium">操作对象：</span>
              {{ getTargetText(selectedLog.targetType) }}
            </div>
            <div>
              <span class="font-medium">IP地址：</span>
              {{ selectedLog.ipAddress || 'N/A' }}
            </div>
            <div>
              <span class="font-medium">操作结果：</span>
              {{ selectedLog.success ? '成功' : '失败' }}
            </div>
          </div>
          
          <div v-if="selectedLog.description">
            <h4 class="font-medium mb-2">操作描述</h4>
            <p class="text-gray-800 bg-gray-50 p-3 rounded">{{ selectedLog.description }}</p>
          </div>
          
          <div v-if="selectedLog.userAgent">
            <h4 class="font-medium mb-2">用户代理</h4>
            <p class="text-gray-800 bg-gray-50 p-3 rounded text-xs font-mono">{{ selectedLog.userAgent }}</p>
          </div>
          
          <div v-if="selectedLog.details">
            <h4 class="font-medium mb-2">详细信息</h4>
            <pre class="text-gray-800 bg-gray-50 p-3 rounded text-xs overflow-x-auto">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../../services/api.js'
import { 
  FileIcon,
  DownloadIcon,
  TrashIcon,
  EyeIcon,
  XIcon
} from 'lucide-vue-next'

// 响应式数据
const logs = ref([])
const loading = ref(false)

// 筛选器
const actionFilter = ref('')
const targetFilter = ref('')
const operatorFilter = ref('')
const ipFilter = ref('')
const timeFilter = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalLogs = ref(0)
const totalPages = ref(0)

// 模态框
const showDetailModal = ref(false)
const selectedLog = ref(null)

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadLogs()
  }, 500)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取操作文本
const getActionText = (action) => {
  const actionMap = {
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'CREATE': '创建',
    'UPDATE': '更新',
    'DELETE': '删除',
    'APPROVE': '审核',
    'BAN': '封禁'
  }
  return actionMap[action] || action
}

// 获取操作颜色
const getActionColor = (action) => {
  const colorMap = {
    'LOGIN': 'badge-success',
    'LOGOUT': 'badge-secondary',
    'CREATE': 'badge-primary',
    'UPDATE': 'badge-warning',
    'DELETE': 'badge-error',
    'APPROVE': 'badge-success',
    'BAN': 'badge-error'
  }
  return colorMap[action] || 'badge-outline'
}

// 获取对象文本
const getTargetText = (target) => {
  const targetMap = {
    'USER': '用户',
    'ARTICLE': '文章',
    'COMMENT': '评论',
    'SYSTEM': '系统'
  }
  return targetMap[target] || target
}

// 加载日志列表
const loadLogs = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      action: actionFilter.value,
      targetType: targetFilter.value,
      operator: operatorFilter.value,
      ip: ipFilter.value,
      timeRange: timeFilter.value
    }
    
    const response = await adminAPI.getLogs(params)
    
    if (response.code === 0 && response.data) {
      if (Array.isArray(response.data)) {
        logs.value = response.data
        totalLogs.value = response.data.length
        totalPages.value = 1
      } else {
        logs.value = response.data.content || []
        totalLogs.value = response.data.totalElements || 0
        totalPages.value = response.data.totalPages || 0
      }
    }
  } catch (err) {
    console.error('Failed to load logs:', err)
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadLogs()
  }
}

// 查看日志详情
const viewLogDetail = (log) => {
  selectedLog.value = log
  showDetailModal.value = true
}

// 导出日志
const exportLogs = async () => {
  try {
    // 这里需要实现日志导出功能
    alert('日志导出功能开发中...')
  } catch (err) {
    console.error('Failed to export logs:', err)
    alert('导出失败')
  }
}

// 清空日志
const clearLogs = async () => {
  if (!confirm('确定要清空所有日志吗？此操作不可恢复！')) return

  try {
    // 这里需要实现清空日志功能
    alert('清空日志功能开发中...')
  } catch (err) {
    console.error('Failed to clear logs:', err)
    alert('清空失败')
  }
}

onMounted(() => {
  loadLogs()
})
</script>
