<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader />

    <div class="container py-6 max-w-4xl">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p class="mt-4 text-gray-600">加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="card">
        <div class="card-content p-8 text-center">
          <p class="text-red-600 mb-4">{{ error }}</p>
          <button @click="loadArticle" class="btn btn-primary">重试</button>
        </div>
      </div>

      <!-- 文章内容 -->
      <div v-else-if="article" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 主内容 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 文章头部 -->
          <div class="card">
            <div class="card-content p-8">
              <div class="space-y-6">
                <div>
                  <span v-if="article.category" class="badge badge-secondary mb-4">
                    {{ article.category.name || article.category }}
                  </span>
                  <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ article.title }}</h1>

                  <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{{ article.viewCount || article.views || 0 }} 浏览</span>
                    <span>{{ formatDate(article.createdAt) }}</span>
                    <span v-if="article.updatedAt && article.updatedAt !== article.createdAt">
                      编辑于 {{ formatDate(article.updatedAt) }}
                    </span>
                  </div>
                </div>

                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div class="avatar w-12 h-12">
                      <img 
                        :src="article.author?.avatar || '/placeholder.svg?height=48&width=48'" 
                        :alt="article.author?.name || '匿名用户'" 
                      />
                    </div>
                    <div>
                      <p class="font-medium">{{ article.author?.name || '匿名用户' }}</p>
                      <p class="text-sm text-gray-600">
                        {{ article.author?.title || article.author?.bio || '用户' }}
                      </p>
                    </div>
                  </div>

                  <button 
                    v-if="article.author && article.author.id !== globalState.user?.id"
                    @click="toggleFollow"
                    :disabled="followLoading"
                    :class="[
                      'btn',
                      article.author.isFollowed ? 'btn-secondary' : 'btn-primary'
                    ]"
                  >
                    {{ followLoading ? '处理中...' : (article.author.isFollowed ? '已关注' : '关注') }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 文章正文 -->
          <div class="card">
            <div class="card-content p-8">
              <div class="prose max-w-none">
                <div class="whitespace-pre-wrap text-gray-800 leading-relaxed" v-html="formatContent(article.content)"></div>
              </div>

              <!-- 文章图片 -->
              <div v-if="article.imageUrls && article.imageUrls.length > 0" class="mt-6">
                <div class="grid grid-cols-2 gap-4">
                  <img
                    v-for="(img, idx) in article.imageUrls"
                    :key="idx"
                    :src="img"
                    alt=""
                    class="rounded-lg object-cover cursor-pointer hover:opacity-90 transition"
                    @click="previewImage(img)"
                  />
                </div>
              </div>

              <!-- 标签 -->
              <div v-if="article.tags && article.tags.length > 0" class="flex flex-wrap gap-2 mt-8 pt-6 border-t">
                <router-link
                  v-for="tag in article.tags"
                  :key="tag"
                  :to="`/topics/${tag}`"
                  class="badge badge-primary hover:bg-blue-100 transition"
                >
                  #{{ tag }}
                </router-link>
              </div>

              <!-- 投票 -->
              <div v-if="article.poll" class="mt-8 p-6 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-2 mb-4">
                  <BarChart3Icon class="h-5 w-5 text-blue-600" />
                  <h3 class="font-medium">{{ article.poll.question }}</h3>
                </div>

                <div class="space-y-3">
                  <div
                    v-for="(option, index) in article.poll.options"
                    :key="index"
                    class="space-y-2"
                  >
                    <button
                      :disabled="article.poll.hasVoted || voteLoading"
                      @click="voteOnPoll(index)"
                      :class="[
                        'w-full justify-start h-auto p-4 transition',
                        article.poll.hasVoted 
                          ? 'btn btn-ghost cursor-default' 
                          : 'btn btn-outline hover:bg-blue-50'
                      ]"
                    >
                      <div class="flex items-center justify-between w-full">
                        <span>{{ option.text || option }}</span>
                        <span v-if="article.poll.hasVoted" class="text-sm text-gray-600">
                          {{ option.votes || 0 }} 票 ({{ option.percentage || 0 }}%)
                        </span>
                      </div>
                    </button>

                    <div v-if="article.poll.hasVoted" class="w-full bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        :style="{ width: `${option.percentage || 0}%` }"
                      />
                    </div>
                  </div>
                </div>

                <p v-if="article.poll.hasVoted" class="text-sm text-gray-600 mt-4">
                  总计 {{ article.poll.totalVotes || 0 }} 人参与投票
                </p>
                
                <p v-if="voteLoading" class="text-sm text-blue-600 mt-4">
                  投票中...
                </p>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center justify-between pt-6 border-t mt-8">
                <div class="flex items-center space-x-6">
                  <button
                    @click="toggleLike"
                    :disabled="likeLoading"
                    :class="[
                      'btn btn-ghost',
                      article.isLiked ? 'text-red-600' : 'text-gray-600 hover:text-red-600'
                    ]"
                  >
                    <HeartIcon :class="['h-4 w-4 mr-1', article.isLiked ? 'fill-current' : '']" />
                    {{ article.likeCount || article.likes || 0 }}
                  </button>
                  
                  <button class="btn btn-ghost text-gray-600">
                    <MessageCircleIcon class="h-4 w-4 mr-1" />
                    {{ comments.length }}
                  </button>
                  
                  <button @click="shareArticle" class="btn btn-ghost text-gray-600 hover:text-green-600">
                    <Share2Icon class="h-4 w-4 mr-1" />
                    分享
                  </button>
                </div>

                <button
                  @click="toggleBookmark"
                  :disabled="bookmarkLoading"
                  :class="[
                    'btn btn-ghost',
                    article.isBookmarked ? 'text-yellow-600' : 'text-gray-600 hover:text-yellow-600'
                  ]"
                >
                  <BookmarkIcon :class="['h-4 w-4', article.isBookmarked ? 'fill-current' : '']" />
                </button>
              </div>
            </div>
          </div>

          <!-- 评论区 -->
          <div class="card">
            <div class="card-content p-6">
              <h3 class="text-lg font-semibold mb-6">
                评论 ({{ comments.length }})
                <button 
                  v-if="comments.length > 0"
                  @click="refreshComments" 
                  :disabled="commentsLoading"
                  class="btn btn-ghost btn-sm ml-2"
                >
                  <RefreshCwIcon :class="['h-4 w-4', commentsLoading ? 'animate-spin' : '']" />
                </button>
              </h3>

              <!-- 发表评论 -->
              <div v-if="globalState.isLoggedIn" class="space-y-4 mb-8">
                <div class="flex items-start space-x-4">
                  <div class="avatar w-10 h-10">
                    <img 
                      :src="globalState.user?.avatar || '/placeholder.svg?height=40&width=40'" 
                      :alt="globalState.user?.name" 
                    />
                  </div>
                  <div class="flex-1">
                    <textarea
                      v-model="newComment"
                      placeholder="写下你的评论..."
                      class="textarea w-full"
                      rows="3"
                      :disabled="commentSubmitting"
                    ></textarea>
                    <div class="flex justify-end mt-2">
                      <button 
                        @click="submitComment"
                        :disabled="!newComment.trim() || commentSubmitting"
                        class="btn btn-primary"
                      >
                        {{ commentSubmitting ? '发表中...' : '发表评论' }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 未登录提示 -->
              <div v-else class="mb-8 p-4 bg-gray-50 rounded-lg text-center">
                <p class="text-gray-600 mb-2">请登录后发表评论</p>
                <router-link to="/login" class="btn btn-primary btn-sm">
                  立即登录
                </router-link>
              </div>

              <div class="border-t pt-6">
                <!-- 评论加载状态 -->
                <div v-if="commentsLoading && comments.length === 0" class="text-center py-8">
                  <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <p class="mt-2 text-gray-600">加载评论中...</p>
                </div>

                <!-- 评论列表 -->
                <div v-else-if="comments.length > 0" class="space-y-6">
                  <CommentItem
                    v-for="comment in comments"
                    :key="comment.id"
                    :comment="comment"
                    :article-id="articleId"
                    @reply="handleReply"
                    @delete="handleDeleteComment"
                    @like="handleLikeComment"
                  />
                </div>

                <!-- 空评论状态 -->
                <div v-else class="text-center py-8">
                  <MessageCircleIcon class="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p class="text-gray-600">还没有评论，来发表第一个评论吧！</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="lg:col-span-1 space-y-6">
          <!-- 作者信息 -->
          <div class="card">
            <div class="card-content p-6">
              <div class="text-center space-y-4">
                <div class="avatar w-16 h-16 mx-auto">
                  <img 
                    :src="article.author?.avatar || '/placeholder.svg?height=64&width=64'" 
                    :alt="article.author?.name || '匿名用户'" 
                  />
                </div>
                <div>
                  <h3 class="font-semibold">{{ article.author?.name || '匿名用户' }}</h3>
                  <p class="text-sm text-gray-600">{{ article.author?.title || article.author?.bio || '用户' }}</p>
                  <div class="flex justify-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>{{ article.author?.followersCount || 0 }} 粉丝</span>
                    <span>{{ article.author?.articlesCount || 0 }} 文章</span>
                  </div>
                </div>
                <button 
                  v-if="article.author && article.author.id !== globalState.user?.id"
                  @click="toggleFollow"
                  :disabled="followLoading"
                  :class="[
                    'btn w-full',
                    article.author.isFollowed ? 'btn-secondary' : 'btn-primary'
                  ]"
                >
                  {{ followLoading ? '处理中...' : (article.author.isFollowed ? '已关注' : '关注') }}
                </button>
              </div>
            </div>
          </div>

          <!-- 相关文章 -->
          <div class="card">
            <div class="card-content p-6">
              <h3 class="font-semibold mb-4">相关文章</h3>
              <div v-if="relatedArticles.length > 0" class="space-y-4">
                <div v-for="relatedArticle in relatedArticles" :key="relatedArticle.id" class="space-y-1">
                  <router-link 
                    :to="`/articles/${relatedArticle.id}`"
                    class="text-sm font-medium hover:text-blue-600 cursor-pointer line-clamp-2 block"
                  >
                    {{ relatedArticle.title }}
                  </router-link>
                  <p class="text-xs text-gray-500">
                    {{ relatedArticle.viewCount || 0 }} 阅读 · {{ formatDate(relatedArticle.createdAt) }}
                  </p>
                </div>
              </div>
              <div v-else class="text-center py-4 text-gray-500">
                暂无相关文章
              </div>
            </div>
          </div>

          <!-- 文章统计 -->
          <div class="card">
            <div class="card-content p-6">
              <h3 class="font-semibold mb-4">文章统计</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-gray-600">浏览量</span>
                  <span class="font-medium">{{ article.viewCount || article.views || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">点赞数</span>
                  <span class="font-medium">{{ article.likeCount || article.likes || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">评论数</span>
                  <span class="font-medium">{{ comments.length }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">收藏数</span>
                  <span class="font-medium">{{ article.bookmarkCount || article.collections || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AppHeader from './AppHeader.vue'
import CommentItem from './CommentItem.vue'
import { contentAPI, commentAPI, socialAPI, pollAPI, userAPI } from '../services/api.js'
import { 
  HeartIcon, 
  MessageCircleIcon, 
  Share2Icon, 
  BookmarkIcon, 
  BarChart3Icon,
  RefreshCwIcon
} from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()
const globalState = inject('globalState')

// 响应式数据
const article = ref(null)
const comments = ref([])
const relatedArticles = ref([])
const newComment = ref('')

// 加载状态
const loading = ref(false)
const commentsLoading = ref(false)
const commentSubmitting = ref(false)
const likeLoading = ref(false)
const bookmarkLoading = ref(false)
const followLoading = ref(false)
const voteLoading = ref(false)
const error = ref('')

// 计算属性
const articleId = computed(() => route.params.id)

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 格式化内容 - 简单的换行处理
const formatContent = (content) => {
  if (!content) return ''
  return content.replace(/\n/g, '<br>')
}

// 加载文章详情
const loadArticle = async () => {
  try {
    loading.value = true
    error.value = ''

    const response = await contentAPI.getArticleById(articleId.value)
    
    if (response.code === 0 && response.data) {
      article.value = response.data
      
      // 加载评论
      loadComments()
      
      // 加载相关文章
      loadRelatedArticles()
    } else {
      throw new Error(response.message || '文章不存在')
    }
  } catch (err) {
    console.error('Failed to load article:', err)
    error.value = err.message || '加载失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 加载评论
const loadComments = async () => {
  try {
    commentsLoading.value = true
    
    const response = await commentAPI.getArticleComments(articleId.value)
    
    if (response.code === 0 && response.data) {
      comments.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (err) {
    console.error('Failed to load comments:', err)
  } finally {
    commentsLoading.value = false
  }
}

// 刷新评论
const refreshComments = () => {
  loadComments()
}

// 加载相关文章
const loadRelatedArticles = async () => {
  try {
    // 这里可以根据标签或分类获取相关文章
    // 暂时使用获取最新文章的接口
    const response = await contentAPI.getArticles(0, 5)
    
    if (response.code === 0 && response.data) {
      const articles = Array.isArray(response.data) ? response.data : response.data.content || []
      // 过滤掉当前文章
      relatedArticles.value = articles.filter(a => a.id != articleId.value).slice(0, 3)
    }
  } catch (err) {
    console.error('Failed to load related articles:', err)
  }
}

// 发表评论
const submitComment = async () => {
  if (!newComment.value.trim()) return

  try {
    commentSubmitting.value = true
    
    const response = await commentAPI.createArticleComment(
      articleId.value,
      newComment.value.trim()
    )
    
    if (response.code === 0) {
      newComment.value = ''
      // 重新加载评论
      loadComments()
    } else {
      throw new Error(response.message || '评论发表失败')
    }
  } catch (err) {
    console.error('Failed to submit comment:', err)
    alert(err.message || '评论发表失败')
  } finally {
    commentSubmitting.value = false
  }
}

// 处理回复
const handleReply = async (commentId, replyText) => {
  try {
    const response = await commentAPI.createArticleComment(
      articleId.value,
      replyText,
      commentId
    )
    
    if (response.code === 0) {
      // 重新加载评论
      loadComments()
    } else {
      throw new Error(response.message || '回复失败')
    }
  } catch (err) {
    console.error('Failed to reply:', err)
    alert(err.message || '回复失败')
  }
}

// 删除评论
const handleDeleteComment = async (commentId) => {
  if (!confirm('确定要删除这条评论吗？')) return

  try {
    const response = await commentAPI.deleteComment(commentId)
    
    if (response.code === 0) {
      // 重新加载评论
      loadComments()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (err) {
    console.error('Failed to delete comment:', err)
    alert(err.message || '删除失败')
  }
}

// 点赞评论
const handleLikeComment = async (commentId, isLiked) => {
  try {
    if (isLiked) {
      await socialAPI.removeLike('COMMENT', commentId)
    } else {
      await socialAPI.toggleLike('COMMENT', commentId)
    }
    
    // 重新加载评论以获取最新状态
    loadComments()
  } catch (err) {
    console.error('Failed to like comment:', err)
  }
}

// 点赞文章
const toggleLike = async () => {
  try {
    likeLoading.value = true
    
    if (article.value.isLiked) {
      await socialAPI.removeLike('ARTICLE', articleId.value)
      article.value.likeCount = (article.value.likeCount || 1) - 1
      article.value.isLiked = false
    } else {
      await socialAPI.toggleLike('ARTICLE', articleId.value)
      article.value.likeCount = (article.value.likeCount || 0) + 1
      article.value.isLiked = true
    }
  } catch (err) {
    console.error('Failed to toggle like:', err)
    alert('操作失败，请稍后重试')
  } finally {
    likeLoading.value = false
  }
}

// 收藏文章
const toggleBookmark = async () => {
  try {
    bookmarkLoading.value = true
    
    if (article.value.isBookmarked) {
      await socialAPI.uncollectArticle(articleId.value)
      article.value.bookmarkCount = (article.value.bookmarkCount || 1) - 1
      article.value.isBookmarked = false
    } else {
      await socialAPI.collectArticle(articleId.value)
      article.value.bookmarkCount = (article.value.bookmarkCount || 0) + 1
      article.value.isBookmarked = true
    }
  } catch (err) {
    console.error('Failed to toggle bookmark:', err)
    alert('操作失败，请稍后重试')
  } finally {
    bookmarkLoading.value = false
  }
}

// 关注/取消关注作者
const toggleFollow = async () => {
  if (!article.value.author) return

  try {
    followLoading.value = true
    
    if (article.value.author.isFollowed) {
      await socialAPI.unfollowUser(article.value.author.id)
      article.value.author.isFollowed = false
      article.value.author.followersCount = (article.value.author.followersCount || 1) - 1
    } else {
      await socialAPI.followUser(article.value.author.id)
      article.value.author.isFollowed = true
      article.value.author.followersCount = (article.value.author.followersCount || 0) + 1
    }
  } catch (err) {
    console.error('Failed to toggle follow:', err)
    alert('操作失败，请稍后重试')
  } finally {
    followLoading.value = false
  }
}

// 投票
const voteOnPoll = async (optionIndex) => {
  if (!article.value.poll || article.value.poll.hasVoted) return

  try {
    voteLoading.value = true
    
    const response = await pollAPI.vote(article.value.poll.id, optionIndex)
    
    if (response.code === 0) {
      // 重新加载文章以获取最新投票结果
      loadArticle()
    } else {
      throw new Error(response.message || '投票失败')
    }
  } catch (err) {
    console.error('Failed to vote:', err)
    alert(err.message || '投票失败')
  } finally {
    voteLoading.value = false
  }
}

// 分享文章
const shareArticle = () => {
  const url = `${window.location.origin}/articles/${articleId.value}`
  const title = article.value.title
  const text = article.value.content.substring(0, 100) + '...'
  
  if (navigator.share) {
    navigator.share({
      title: title,
      text: text,
      url: url
    }).catch(err => console.log('分享失败:', err))
  } else {
    // 复制到剪贴板
    navigator.clipboard.writeText(url).then(() => {
      alert('文章链接已复制到剪贴板')
    }).catch(() => {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = url
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('文章链接已复制到剪贴板')
    })
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  // 简单的图片预览，可以后续扩展为模态框
  window.open(imageUrl, '_blank')
}

// 组件挂载时加载数据
onMounted(() => {
  loadArticle()
})
</script>

<style scoped>
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.prose {
  line-height: 1.7;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.prose p {
  margin-bottom: 1em;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}
</style>
